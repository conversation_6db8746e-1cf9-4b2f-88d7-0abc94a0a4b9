# DnsLookupUtil - DNS Lookup Utility Class

## Overview

The `DnsLookupUtil` class is a comprehensive Java utility for DNS lookup operations using the DNSJava library. It provides static methods for common DNS operations and automatically discovers DNS servers from the system's Network Interface Cards (NICs).

## Features

### Core DNS Operations
- **A Record Lookups**: Resolve hostnames to IPv4 addresses
- **AAAA Record Lookups**: Resolve hostnames to IPv6 addresses  
- **MX Record Lookups**: Find mail exchange servers for domains
- **CNAME Record Lookups**: Find canonical names for hostnames
- **PTR Record Lookups**: Reverse DNS lookups (IP to hostname)

### DNS Server Discovery
- Automatically discovers DNS servers from system configuration
- Reads `/etc/resolv.conf` on Linux/Unix systems
- Attempts to infer DNS servers from network interface configurations
- Falls back to well-known public DNS servers if no system servers found
- <PERSON><PERSON><PERSON> discovered DNS servers for 5 minutes to improve performance

### Error Handling
- Comprehensive exception handling for DNS resolution failures
- Timeout handling for network issues
- Input validation with meaningful error messages
- Graceful fallback mechanisms

## Dependencies

The utility requires the DNSJava library:

```xml
<dependency>
    <groupId>dnsjava</groupId>
    <artifactId>dnsjava</artifactId>
    <version>3.5.3</version>
</dependency>
```

## Usage Examples

### A Record Lookup (IPv4)
```java
// Basic A record lookup
List<String> ipAddresses = DnsLookupUtil.lookupA("google.com");
for (String ip : ipAddresses) {
    System.out.println("IPv4: " + ip);
}

// A record lookup with custom timeout
List<String> ipAddresses = DnsLookupUtil.lookupA("google.com", 10);
```

### AAAA Record Lookup (IPv6)
```java
// Basic AAAA record lookup
List<String> ipv6Addresses = DnsLookupUtil.lookupAAAA("google.com");
for (String ip : ipv6Addresses) {
    System.out.println("IPv6: " + ip);
}

// AAAA record lookup with custom timeout
List<String> ipv6Addresses = DnsLookupUtil.lookupAAAA("google.com", 15);
```

### MX Record Lookup
```java
// MX record lookup
List<String> mxRecords = DnsLookupUtil.lookupMX("google.com");
for (String mx : mxRecords) {
    System.out.println("MX: " + mx); // Format: "priority hostname"
}
```

### CNAME Record Lookup
```java
// CNAME record lookup
Optional<String> cname = DnsLookupUtil.lookupCNAME("www.github.com");
if (cname.isPresent()) {
    System.out.println("CNAME: " + cname.get());
}
```

### PTR Record Lookup (Reverse DNS)
```java
// PTR record lookup
Optional<String> hostname = DnsLookupUtil.lookupPTR("*******");
if (hostname.isPresent()) {
    System.out.println("Hostname: " + hostname.get());
}
```

### DNS Server Discovery
```java
// Discover DNS servers
List<String> dnsServers = DnsLookupUtil.discoverDnsServers();
System.out.println("DNS Servers: " + dnsServers);

// Get cached DNS servers
List<String> cached = DnsLookupUtil.getCachedDnsServers();

// Clear DNS cache
DnsLookupUtil.clearDnsCache();
```

## Method Reference

### A Record Methods
- `lookupA(String hostname)` - Basic A record lookup
- `lookupA(String hostname, int timeoutSeconds)` - A record lookup with timeout

### AAAA Record Methods  
- `lookupAAAA(String hostname)` - Basic AAAA record lookup
- `lookupAAAA(String hostname, int timeoutSeconds)` - AAAA record lookup with timeout

### MX Record Methods
- `lookupMX(String domain)` - Basic MX record lookup
- `lookupMX(String domain, int timeoutSeconds)` - MX record lookup with timeout

### CNAME Record Methods
- `lookupCNAME(String hostname)` - Basic CNAME record lookup
- `lookupCNAME(String hostname, int timeoutSeconds)` - CNAME record lookup with timeout

### PTR Record Methods
- `lookupPTR(String ipAddress)` - Basic PTR record lookup
- `lookupPTR(String ipAddress, int timeoutSeconds)` - PTR record lookup with timeout

### DNS Server Management
- `discoverDnsServers()` - Discover and cache DNS servers
- `getCachedDnsServers()` - Get currently cached DNS servers
- `clearDnsCache()` - Clear DNS server cache

## Return Types

- **List&lt;String&gt;**: Used for methods that can return multiple results (A, AAAA, MX records)
- **Optional&lt;String&gt;**: Used for methods that return single results (CNAME, PTR records)
- **Empty collections/Optional**: Returned when no DNS records are found

## Error Handling

The utility handles various error conditions:

- **IllegalArgumentException**: Thrown for invalid input parameters
- **Network timeouts**: Handled gracefully with configurable timeouts
- **DNS resolution failures**: Logged and return empty results
- **Missing DNS servers**: Falls back to public DNS servers

## Configuration

### Default Settings
- **Timeout**: 5 seconds
- **Retries**: 2 attempts
- **DNS Cache Validity**: 5 minutes
- **Fallback DNS Servers**: Google DNS (*******, *******), Cloudflare (*******, *******), OpenDNS

### DNS Server Discovery Order
1. Read from `/etc/resolv.conf` (Linux/Unix)
2. Infer from network interface configurations
3. Use fallback public DNS servers

## Testing

The utility includes comprehensive unit tests covering:
- All DNS record types (A, AAAA, MX, CNAME, PTR)
- DNS server discovery functionality
- Error handling and edge cases
- Input validation
- Timeout behavior

Tests are integrated into the existing `TestMiscellaneous` class.

## Thread Safety

The utility is thread-safe:
- All methods are static
- DNS server cache uses volatile variables
- No shared mutable state between method calls

## Performance Considerations

- DNS server discovery results are cached for 5 minutes
- Uses connection pooling through DNSJava library
- Configurable timeouts to prevent hanging operations
- Efficient stream processing for result filtering

## Integration

The utility integrates seamlessly with the existing Motadata codebase:
- Uses the standard Logger pattern
- Follows existing naming conventions
- Compatible with the current error handling approach
- Designed as a static utility class like other utilities in the project
