package com.mindarray;

import io.netty.resolver.dns.DefaultDnsServerAddressStreamProvider;
import io.netty.resolver.dns.DnsServerAddressStream;
import io.vertx.core.Vertx;
import io.vertx.core.dns.DnsClient;
import io.vertx.core.dns.DnsClientOptions;
import org.xbill.DNS.ResolverConfig;

import java.net.InetSocketAddress;
import java.util.ArrayList;
import java.util.Arrays;

import java.util.List;

public class TestDNS {

    public static void main(String[] args) {

        ResolverConfig config = ResolverConfig.getCurrentConfig();

        List<String> servers = Arrays.asList(config.servers().toString());

        System.out.println("System DNS Servers:");

        servers.forEach(System.out::println);

        Vertx vertx = Vertx.vertx();

        var dnsClient = vertx.createDnsClient(new DnsClientOptions().setRecursionDesired(true));

        InetSocketAddress dnsAddress = getSystemDnsServers().get(0);

        DnsClient client = vertx.createDnsClient(
                new DnsClientOptions()
                        .setHost(dnsAddress.getAddress().getHostAddress())
                        .setPort(dnsAddress.getPort() == 0 ? 53 : dnsAddress.getPort())
        );
    }

    public static List<InetSocketAddress> getSystemDnsServers() {
        DefaultDnsServerAddressStreamProvider provider = DefaultDnsServerAddressStreamProvider.INSTANCE;

        // "." refers to the root domain, gives default DNS servers used for all domains
        DnsServerAddressStream stream = provider.nameServerAddressStream(".");

        List<InetSocketAddress> dnsServers = new ArrayList<>();
        for (int i = 0; i < stream.size(); i++) {
            dnsServers.add((InetSocketAddress) stream.next());
        }
        return dnsServers;
    }

}

