/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs
 *  Date            Author          Notes
 *  10-July-2025    Nikunj          MOTADATA-6659: Changed packGridResult and packChartResult to packResult
 */

package com.mindarray.visualization;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.flow.FlowEngineConstants;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.GeoDBUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;

public class VisualizationEventResponseProcessor extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(VisualizationEventResponseProcessor.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization Event Response Processor");

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_VISUALIZATION_EVENT_RESPONSE_PROCESSOR, message ->
            {
                if (!message.body().isEmpty())
                {
                    var event = message.body();

                    try
                    {
                        var context = event.getJsonObject(QUERY_CONTEXT);

                        if (context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.MAP.getName()))
                        {

                            var result = composeGeoMapResponse(event.getJsonObject(RESULT).getJsonObject(CommonUtil.getString(event.getValue(SUB_QUERY_ID))), context);

                            if (!result.isEmpty())
                            {
//                                vertx.eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(result, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), event.getLong(VisualizationConstants.QUERY_ID), event.getLong(SUB_QUERY_ID)).getBytes()));
                                vertx.eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packResult(result, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), event.getLong(VisualizationConstants.QUERY_ID), event.getLong(SUB_QUERY_ID)).getBytes()));
                            }

                            else
                            {
                                //send back empty result

                                VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), VisualizationConstants.VisualizationCategory.GRID.getName(), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, "Preview Widget", ErrorMessageConstants.INVALID_DATA_SOURCE), event.getLong(VisualizationConstants.QUERY_ID), event.getLong(VisualizationConstants.SUB_QUERY_ID), LOGGER, EventBusConstants.EVENT_VISUALIZATION_RESPONSE);
                            }
                        }

                        else
                        {
                            composeQueryResponse(event.getJsonObject("column.mappers", new JsonObject()), event.getInteger(QUERY_PROGRESS), event.getJsonObject(RESULT), event.getString(VISUALIZATION_CATEGORY, VisualizationCategory.GRID.getName()), event.getLong(QUERY_ID), event.getLong(VisualizationConstants.SUB_QUERY_ID), context, LOGGER, event.getJsonObject(QUERY_CONTEXT).containsKey(DUMMY_FIELDS) ? event.getJsonObject(QUERY_CONTEXT).getBoolean(DUMMY_FIELDS) : false);
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    private JsonObject composeGeoMapResponse(JsonObject visualizationResult, JsonObject context)
    {
        var result = new JsonObject();

        try
        {

            var groupType = context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getString(TYPE);

            var rows = visualizationResult.getJsonArray(RESULT);

            if (rows != null)
            {
                if (groupType.equalsIgnoreCase(VisualizationDataSource.FLOW.getName()))
                {
                    for (var i = 0; i < rows.size(); i++)
                    {
                        var skip = false;

                        var row = rows.getJsonObject(i);

                        JsonObject geoLocation;

                        if (row.containsKey(FlowEngineConstants.SOURCE_CITY))
                        {
                            if (!row.getString(FlowEngineConstants.SOURCE_CITY).equalsIgnoreCase(UNKNOWN.toLowerCase()))
                            {
                                geoLocation = GeoDBUtil.getCityGeoLocation(row.getString(FlowEngineConstants.SOURCE_CITY));

                                if (geoLocation != null)
                                {
                                    row.put(FlowEngineConstants.SOURCE_LATITUDE, CommonUtil.getString(geoLocation.getValue(LATITUDE))).put(FlowEngineConstants.SOURCE_LONGITUDE, CommonUtil.getString(geoLocation.getValue(LONGITUDE)));
                                }

                                else
                                {
                                    row.put(FlowEngineConstants.SOURCE_LATITUDE, EMPTY_VALUE).put(FlowEngineConstants.SOURCE_LONGITUDE, EMPTY_VALUE);
                                }
                            }

                            else
                            {
                                skip = true;
                            }
                        }

                        if (!skip && row.containsKey(FlowEngineConstants.DESTINATION_CITY))
                        {

                            if (!row.getString(FlowEngineConstants.DESTINATION_CITY).equalsIgnoreCase(UNKNOWN.toLowerCase()))
                            {
                                geoLocation = GeoDBUtil.getCityGeoLocation(row.getString(FlowEngineConstants.DESTINATION_CITY));

                                if (geoLocation != null)
                                {
                                    row.put(FlowEngineConstants.DESTINATION_LATITUDE, CommonUtil.getString(geoLocation.getValue(LATITUDE))).put(FlowEngineConstants.DESTINATION_LONGITUDE, CommonUtil.getString(geoLocation.getValue(LONGITUDE)));
                                }

                                else
                                {
                                    row.put(FlowEngineConstants.DESTINATION_LATITUDE, EMPTY_VALUE).put(FlowEngineConstants.DESTINATION_LONGITUDE, EMPTY_VALUE);
                                }

                            }
                            else
                            {
                                skip = true;
                            }
                        }

                        if (!skip && row.containsKey(FlowEngineConstants.SOURCE_COUNTRY) && (!row.containsKey(FlowEngineConstants.SOURCE_LATITUDE) || row.getString(FlowEngineConstants.SOURCE_LATITUDE).isEmpty()))
                        {
                            if (!row.getString(FlowEngineConstants.SOURCE_COUNTRY).equalsIgnoreCase(UNKNOWN.toLowerCase()))
                            {
                                geoLocation = GeoDBUtil.getCountryGeoLocation(row.getString(FlowEngineConstants.SOURCE_COUNTRY));

                                if (geoLocation != null)
                                {
                                    row.put(FlowEngineConstants.COUNTRY_CODE, CommonUtil.getString(geoLocation.getValue(FlowEngineConstants.COUNTRY_CODE, EMPTY_VALUE))).put(FlowEngineConstants.SOURCE_LATITUDE, CommonUtil.getString(geoLocation.getValue(LATITUDE))).put(FlowEngineConstants.SOURCE_LONGITUDE, CommonUtil.getString(geoLocation.getValue(LONGITUDE)));
                                }

                                else
                                {
                                    row.put(FlowEngineConstants.COUNTRY_CODE, EMPTY_VALUE).put(FlowEngineConstants.SOURCE_LATITUDE, EMPTY_VALUE).put(FlowEngineConstants.SOURCE_LONGITUDE, EMPTY_VALUE);
                                }
                            }

                            else
                            {
                                skip = true;
                            }
                        }

                        if (!skip && row.containsKey(FlowEngineConstants.DESTINATION_COUNTRY) && (!row.containsKey(FlowEngineConstants.DESTINATION_LATITUDE) || row.getString(FlowEngineConstants.DESTINATION_LATITUDE).isEmpty()))
                        {
                            if (!row.getString(FlowEngineConstants.DESTINATION_COUNTRY).equalsIgnoreCase(UNKNOWN.toLowerCase()))
                            {
                                geoLocation = GeoDBUtil.getCountryGeoLocation(row.getString(FlowEngineConstants.DESTINATION_COUNTRY));

                                if (geoLocation != null)
                                {
                                    row.put(FlowEngineConstants.COUNTRY_CODE, CommonUtil.getString(geoLocation.getValue(FlowEngineConstants.COUNTRY_CODE, EMPTY_VALUE))).put(FlowEngineConstants.DESTINATION_LATITUDE, CommonUtil.getString(geoLocation.getValue(LATITUDE))).put(FlowEngineConstants.DESTINATION_LONGITUDE, CommonUtil.getString(geoLocation.getValue(LONGITUDE)));
                                }

                                else
                                {
                                    row.put(FlowEngineConstants.COUNTRY_CODE, EMPTY_VALUE).put(FlowEngineConstants.DESTINATION_LATITUDE, EMPTY_VALUE).put(FlowEngineConstants.DESTINATION_LONGITUDE, EMPTY_VALUE);
                                }
                            }

                            else
                            {
                                skip = true;
                            }
                        }

                        if (!skip)
                        {
                            compose(row, result);
                        }
                    }
                }

                else
                {

                    var columns = context.getJsonArray(VISUALIZATION_RESULT_BY);

                    for (var i = 0; i < rows.size(); i++)
                    {
                        var skip = false;

                        var row = rows.getJsonObject(i);

                        JsonObject location;

                        for (var j = 0; j < columns.size(); j++)
                        {
                            var column = columns.getString(j);

                            var destination = column.contains("destination");

                            var city = column.endsWith(".city");

                            if (row.containsKey(column) && !row.getString(column).isEmpty())
                            {
                                if (!destination && city)//if source city is provided then providing priority ot city
                                {
                                    location = GeoDBUtil.getCityGeoLocation(row.getString(column));

                                    if (location != null)
                                    {
                                        row.put(FlowEngineConstants.SOURCE_LATITUDE, CommonUtil.getString(location.getValue(LATITUDE))).put(FlowEngineConstants.SOURCE_LONGITUDE, CommonUtil.getString(location.getValue(LONGITUDE)));
                                    }

                                    else
                                    {
                                        row.put(FlowEngineConstants.SOURCE_LATITUDE, EMPTY_VALUE).put(FlowEngineConstants.SOURCE_LONGITUDE, EMPTY_VALUE);
                                    }
                                }

                                if (destination && city)//if destination city is provided then providing priority ot city
                                {
                                    location = GeoDBUtil.getCityGeoLocation(row.getString(column));

                                    if (location != null)
                                    {
                                        row.put(FlowEngineConstants.DESTINATION_LATITUDE, CommonUtil.getString(location.getValue(LATITUDE))).put(FlowEngineConstants.DESTINATION_LONGITUDE, CommonUtil.getString(location.getValue(LONGITUDE)));
                                    }
                                    else
                                    {
                                        row.put(FlowEngineConstants.DESTINATION_LATITUDE, EMPTY_VALUE).put(FlowEngineConstants.DESTINATION_LONGITUDE, EMPTY_VALUE);
                                    }
                                }

                                if (!city && !destination && (!row.containsKey(FlowEngineConstants.SOURCE_LATITUDE) || row.getString(FlowEngineConstants.SOURCE_LATITUDE).isEmpty())) //if source country is provided then checking whther city is selected and its latitude longitude is blank so resolving country
                                {
                                    location = GeoDBUtil.getCountryGeoLocation(row.getString(column));

                                    if (location != null)
                                    {
                                        row.put(FlowEngineConstants.COUNTRY_CODE, CommonUtil.getString(location.getValue(FlowEngineConstants.COUNTRY_CODE, EMPTY_VALUE))).put(FlowEngineConstants.SOURCE_LATITUDE, CommonUtil.getString(location.getValue(LATITUDE))).put(FlowEngineConstants.SOURCE_LONGITUDE, CommonUtil.getString(location.getValue(LONGITUDE)));
                                    }

                                    else
                                    {
                                        row.put(FlowEngineConstants.COUNTRY_CODE, EMPTY_VALUE).put(FlowEngineConstants.SOURCE_LATITUDE, EMPTY_VALUE).put(FlowEngineConstants.SOURCE_LONGITUDE, EMPTY_VALUE);
                                    }
                                }

                                if (!city && destination && (!row.containsKey(FlowEngineConstants.DESTINATION_LATITUDE) || row.getString(FlowEngineConstants.DESTINATION_LATITUDE).isEmpty()))  //if destination country is provided then checking whther city is selected and its latitude longitude is blank so resolving country
                                {
                                    location = GeoDBUtil.getCountryGeoLocation(row.getString(column));

                                    if (location != null)
                                    {
                                        row.put(FlowEngineConstants.COUNTRY_CODE, CommonUtil.getString(location.getValue(FlowEngineConstants.COUNTRY_CODE, EMPTY_VALUE))).put(FlowEngineConstants.DESTINATION_LATITUDE, CommonUtil.getString(location.getValue(LATITUDE))).put(FlowEngineConstants.DESTINATION_LONGITUDE, CommonUtil.getString(location.getValue(LONGITUDE)));
                                    }

                                    else
                                    {
                                        row.put(FlowEngineConstants.COUNTRY_CODE, EMPTY_VALUE).put(FlowEngineConstants.DESTINATION_LATITUDE, EMPTY_VALUE).put(FlowEngineConstants.DESTINATION_LONGITUDE, EMPTY_VALUE);
                                    }
                                }
                            }

                            else
                            {
                                skip = true;

                                break;
                            }
                        }

                        if (!skip && ((row.containsKey(FlowEngineConstants.SOURCE_LATITUDE) && row.getString(FlowEngineConstants.SOURCE_LATITUDE).isEmpty()) || (row.containsKey(FlowEngineConstants.SOURCE_LONGITUDE) && row.getString(FlowEngineConstants.SOURCE_LONGITUDE).isEmpty()) ||
                                (row.containsKey(FlowEngineConstants.DESTINATION_LATITUDE) && row.getString(FlowEngineConstants.DESTINATION_LATITUDE).isEmpty()) || (row.containsKey(FlowEngineConstants.DESTINATION_LONGITUDE) && row.getString(FlowEngineConstants.DESTINATION_LONGITUDE).isEmpty())))
                        {
                            skip = true;
                        }

                        if (!skip)
                        {
                            compose(row, result);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }
}
