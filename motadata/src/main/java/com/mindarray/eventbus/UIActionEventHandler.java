/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author			Notes
 *  10-Feb-2025		Chandresh		MOTADATA-4905: refactored UI_ACTION_RUNBOOK_PLUGIN_TEST to support running multiple monitors while testing runbook in single event
 *  14-Feb-2025     Chandresh       MOTADATA-4822: Updated LDAP connection test to support secondary LDAP host
 *  24-Feb-2025		Chandresh		MOTADATA-3680: Added UI_ACTION_RUNBOOK_PLUGIN_RUN handler to support running runbooks with multiple instances
 *  26-Feb-2025     Chandresh       MOTADATA-5216: Runbook | Proper status and error message should be shown after testing any runbook
 *  28-Feb-2025     Smit            MOTADATA-4956: Rule Based Tagging, Preview and Run Events
 *  3-Mar-2025      Chandresh       MOTADATA-4822: ldap.server.host -> ldap.server.primary.host
 *  28-Feb-2025     Bharat          MOTADATA-5233: Added event ui.action.share for share feature
 *  4-Mar-2025      Bharat          MOTADATA-4740: Two factor authentication 2FA
 *  18-Mar-2025     Smit            MOTADATA-5431: Module Level Logging
 *  20-Mar-2025     Chopra Deven    MOTADATA-5370: Added event to get current flap of policy in Alert Drill-Down
 *  19-Mar-2025     Pruthviraj      MOTADATA-5331: NetRoute policy ui.actions added
 *  9-Apr-2025      Bharat          MOTADATA-5141: Alert Drill-down from email and Teams Notification
 *  24-Mar-2025     Chandresh       MOTADATA-5426: Docker discovery and polling support added
 *  23-Apr-2025     Bharat          MOTADATA-5822: Fixed session view and default policy view bug
 *  19-May-2025     Sankalp         MOTADATA-5939: Cisco ACI Topology support
 *  24-Jun-2025     Yash Tiwari     MOTADATA-6528 : refactored UI_ACTION_TOPOLOGY_HIERARCHY_FETCH to fetch left hierarchy from DQP
 */

package com.mindarray.eventbus;

import com.mindarray.*;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.api.MetricPlugin;
import com.mindarray.config.ConfigConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.integration.IntegrationConstants;
import com.mindarray.integration.IntegrationEngine;
import com.mindarray.integration.TeamsIntegration;
import com.mindarray.job.JobScheduler;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.log.LogPatternDetector;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.report.ReportConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.*;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import com.opencsv.CSVWriterBuilder;
import com.unboundid.ldap.sdk.LDAPConnection;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.net.NetClientOptions;
import io.vertx.core.net.ProxyOptions;
import io.vertx.ext.mail.*;
import io.vertx.ext.web.client.WebClient;
import io.vertx.ext.web.client.WebClientOptions;
import io.vertx.ext.web.handler.sockjs.impl.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.apache.http.HttpStatus;
import org.apache.lucene.analysis.core.KeywordAnalyzer;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.store.FSDirectory;

import java.io.BufferedReader;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipFile;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.GlobalConstants.DURATION;
import static com.mindarray.InfoMessageConstants.EMAIL_ID_VALIDATED;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.Agent.AGENT_VERSION;
import static com.mindarray.api.CredentialProfile.CREDENTIAL_PROFILE_CONTEXT;
import static com.mindarray.api.Discovery.DISCOVERY_CONTEXT;
import static com.mindarray.api.LDAPServer.LDAP_SERVER_SECONDARY_HOST;
import static com.mindarray.api.LogParser.*;
import static com.mindarray.api.MotadataApp.ARTIFACT_TYPE;
import static com.mindarray.api.MotadataApp.MAX_VERSION_FILE_SIZE_BYTES;
import static com.mindarray.api.RemoteEventProcessor.*;
import static com.mindarray.api.RunbookPlugin.*;
import static com.mindarray.api.User.USER_EMAIL;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.log.LogEngineConstants.LogParserType.REGEX;
import static com.mindarray.log.LogPatternDetector.END_POS;
import static com.mindarray.log.LogPatternDetector.START_POS;
import static com.mindarray.manager.MotadataAppManager.PATCH_ARTIFACT_FILE;
import static com.mindarray.nms.NMSConstants.*;
import static com.mindarray.nms.SNMPTrapProcessor.SNMP_TRAP_OID;
import static com.mindarray.notification.Notification.NOTIFICATION_TYPE;
import static com.mindarray.policy.PolicyEngineConstants.*;
import static com.mindarray.report.ReportConstants.REPORT_ID;
import static com.mindarray.visualization.VisualizationConstants.MAX_RECORDS;
import static com.mindarray.visualization.VisualizationConstants.TIMESTAMP;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;


/**
 * The UIActionEventHandler class handles events triggered by user interface actions and routes them to appropriate handlers.
 * <p>
 * This class is responsible for:
 * <ul>
 *   <li>Processing UI-initiated events and actions</li>
 *   <li>Routing events to appropriate handlers based on event type</li>
 *   <li>Enriching events with additional context information</li>
 *   <li>Managing notification delivery through various channels (email, integration channels)</li>
 *   <li>Validating user inputs and configurations</li>
 *   <li>Executing administrative actions (user management, configuration testing, etc.)</li>
 *   <li>Handling file uploads and downloads</li>
 * </ul>
 * <p>
 * The handler acts as a bridge between the user interface and the backend systems, translating
 * user actions into system events and routing them to the appropriate components for processing.
 * It also handles the responses from these components and formats them for presentation to the user.
 * <p>
 * The class supports a wide range of UI actions including:
 * <ul>
 *   <li>User authentication and authorization</li>
 *   <li>Configuration testing (credentials, connections, etc.)</li>
 *   <li>Report generation and export</li>
 *   <li>Notification management</li>
 *   <li>File uploads and downloads</li>
 *   <li>System administration tasks</li>
 * </ul>
 * <p>
 * Example usage:
 * <pre>
 * // Deploy the UIActionEventHandler
 * vertx.deployVerticle(new UIActionEventHandler());
 *
 * // Send a UI action event
 * JsonObject event = new JsonObject()
 *     .put("action", "test-credential")
 *     .put("credential", credentialJson)
 *     .put("session_id", sessionId);
 *
 * vertx.eventBus().send("UI_ACTION_CONFIGURATION_TEST", event, reply -> {
 *     if (reply.succeeded()) {
 *         // Handle successful response
 *         JsonObject result = reply.result().body();
 *         System.out.println("Test result: " + result.getString("status"));
 *     } else {
 *         // Handle failure
 *         System.err.println("Test failed: " + reply.cause().getMessage());
 *     }
 * });
 * </pre>
 */
public class UIActionEventHandler extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(UIActionEventHandler.class, GlobalConstants.MOTADATA_EVENT_BUS, "UI Action Event Handler");

    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions().setSendTimeout(300000L);

    private long timer = 0L;

    private static void setRecipients(JsonArray recipients, Set<String> emailRecipients, Map<String, Set<String>> channelRecipients)
    {
        //gathering unique email or channels
        for (var index = 0; index < recipients.size(); index++)
        {
            var recipient = recipients.getJsonObject(index);

            if (recipient.getString(PolicyEngineConstants.RECIPIENT_TYPE, EMPTY_VALUE).equalsIgnoreCase(PolicyEngineConstants.RecipientType.EMAIl.getName()))
            {
                emailRecipients.add(recipient.getString(PolicyEngineConstants.RECIPIENT));
            }
            else if (recipient.getString(PolicyEngineConstants.RECIPIENT_TYPE, EMPTY_VALUE).equalsIgnoreCase(PolicyEngineConstants.RecipientType.CHANNEL.getName()))
            {
                var item = IntegrationConfigStore.getStore().getItem(recipient.getLong(IntegrationProfile.INTEGRATION));

                if (item != null)
                {
                    channelRecipients.computeIfAbsent(item.getString(Integration.INTEGRATION_TYPE), value -> new HashSet<>())
                            .add(recipient.getString(ID));
                }
            }
            else if (recipient.getString(PolicyEngineConstants.RECIPIENT_TYPE, EMPTY_VALUE).equalsIgnoreCase(PolicyEngineConstants.RecipientType.USER.getName()))
            {
                var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, CommonUtil.getString(recipient.getString(PolicyEngineConstants.RECIPIENT)));

                if (item != null && !item.isEmpty())
                {
                    if (item.containsKey(User.USER_EMAIL) && !item.getString(User.USER_EMAIL).isEmpty())
                    {
                        emailRecipients.add(item.getString(User.USER_EMAIL));
                    }
                }
            }
        }
    }

    private static JsonObject enrich(JsonObject event)
    {
        var context = new JsonObject();

        context.mergeIn(event).put(MESSAGE, event.getString(MESSAGE, EMPTY_VALUE)).put(User.USER_NAME, event.getString(User.USER_NAME));

        if (event.containsKey(PolicyEngineConstants.POLICY_TYPE) && !event.getString(PolicyEngineConstants.POLICY_TYPE).isEmpty())
        {
            if ((PolicyEngineConstants.PolicyType.TRAP.getName().equalsIgnoreCase(event.getString(PolicyEngineConstants.POLICY_TYPE))
                    || PolicyEngineConstants.PolicyType.LOG.getName().equalsIgnoreCase(event.getString(PolicyEngineConstants.POLICY_TYPE))
                    || PolicyEngineConstants.PolicyType.FLOW.getName().equalsIgnoreCase(event.getString(PolicyEngineConstants.POLICY_TYPE)))
                    && EventPolicyConfigStore.getStore().existItem(event.getLong(POLICY_ID)))
            {
                context.mergeIn(EventPolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID)));

                context.put(PolicyEngineConstants.ACTIVE_SINCE, DateTimeUtil.getTimeStampFromEpochTimestamp(event.getLong("last.seen"), true));
            }
            else if (MetricPolicyConfigStore.getStore().existItem(event.getLong(POLICY_ID)))
            {
                context.mergeIn(MetricPolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID)));

                if (ObjectConfigStore.getStore().existItem(event.getLong(Entity.OBJECT.getName().toLowerCase())))
                {
                    context.mergeIn(ObjectConfigStore.getStore().getItem(event.getLong(Entity.OBJECT.getName().toLowerCase())));
                }

                context.put(PolicyEngineConstants.ACTIVE_SINCE, DateTimeUtil.timestamp(DateTimeUtil.currentMilliSeconds() - (event.getLong(PolicyEngineConstants.DURATION) * 1000)));

                context.put(PolicyEngineConstants.POLICY_MESSAGE, String.format("%s has entered into %s state with value %s on %s(%s)", context.getString(METRIC, EMPTY_VALUE), context.getString(SEVERITY, EMPTY_VALUE).toLowerCase(), context.getString(VALUE, EMPTY_VALUE), context.getString(OBJECT_HOST, EMPTY_VALUE), context.getString(OBJECT_IP, EMPTY_VALUE)));
            }

            var actions = context.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS, new JsonObject());

            var items = new HashSet<String>();

            if (!actions.isEmpty() && actions.getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK.getName()) != null && !actions.getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK.getName()).isEmpty()
                    && actions.getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK.getName()).containsKey(context.getString(SEVERITY, EMPTY_VALUE)) && !actions.getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(context.getString(SEVERITY, EMPTY_VALUE)).isEmpty())
            {
                var entries = actions.getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(context.getString(SEVERITY, EMPTY_VALUE));

                for (var index = 0; index < entries.size(); index++)
                {
                    if (RunbookPluginConfigStore.getStore().existItem(entries.getJsonObject(index).getLong(ID)))
                    {
                        items.add(RunbookPluginConfigStore.getStore().getItem(entries.getJsonObject(index).getLong(ID)).getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME));
                    }
                }
            }

            context.put("policy.action", items.isEmpty() ? EMPTY_VALUE : String.join(COMMA_SEPARATOR, items));

            if (context.containsKey(PolicyEngineConstants.POLICY_CONTEXT) && context.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT) != null)
            {
                context.mergeIn(context.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT));

                context.remove(PolicyEngineConstants.POLICY_CONTEXT);
            }

            context.put(PolicyEngineConstants.EVENT_FIELD, context.getString(METRIC));

            context.put(TIMESTAMP, DateTimeUtil.getTimeStampFromEpochTimestamp(event.getLong(TIMESTAMP), false));

            context.put(SEVERITY, context.getString(SEVERITY).toLowerCase());
        }

        // As search query have " in it's replacing it with ' as json decode by Microsoft Teams API is not possible
        if (context.containsKey("search.query"))
        {
            context.put("search.query", context.getString("search.query").replace("\"", "'"));
        }

        return context;
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().<JsonObject>localConsumer("test", message ->
        {
            var id = ObjectConfigStore.getStore().getItemByIP("*************");

            Bootstrap.configDBService().update(DBConstants.TBL_OBJECT, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, id),
                    new JsonObject().put(OBJECT_IP, "*************"), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            ObjectConfigStore.getStore().updateItem(id);
                        }
                    });
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_CONFIGURATION_TEST, message ->
        {

            var event = message.body();

            var eventId = CommonUtil.newEventId();

            var asyncResult = Promise.<JsonObject>promise();

            vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, message.body())
                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_ACTION_CONFIGURATION_TEST)
                    .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                    .put(USER_NAME, event.getString(USER_NAME))
                    .put(EventBusConstants.EVENT_ID, eventId));

            try
            {

                var primaryHost = event.getString(LDAPServer.LDAP_SERVER_PRIMARY_HOST);

                var userName = event.getString(LDAPServer.LDAP_SERVER_USERNAME);

                var password = event.getString(LDAPServer.LDAP_SERVER_PASSWORD);

                var domain = event.getString(LDAPServer.LDAP_SERVER_FQDN);

                var port = event.getInteger(LDAPServer.LDAP_SERVER_PORT);

                if (CommonUtil.isNullOrEmpty(primaryHost) || CommonUtil.isNullOrEmpty(domain) || CommonUtil.isNullOrEmpty(userName) || CommonUtil.isNullOrEmpty(password) || port == null)
                {
                    asyncResult.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.LDAP_SERVER_TEST_FAILED_INVALID_EVENT_PARAMETERS));

                }

                else
                {

                    vertx.<Map<String, Object>>executeBlocking(future ->
                    {
                        EventBusConstants.startEvent(eventId, Thread.currentThread().getName());

                        future.complete(
                                LDAPUtil.getLDAPConnection(primaryHost, event.getString(LDAP_SERVER_SECONDARY_HOST), port, userName, password, domain, event.getString(LDAPServer.LDAP_SERVER_PROTOCOL), event.getString(LDAPServer.LDAP_SERVER_TYPE)));
                    }, result ->
                    {

                        if (result.succeeded())
                        {
                            var ldapConnection = (LDAPConnection) result.result().get(RESULT);

                            if (ldapConnection != null)
                            {
                                asyncResult.complete(event.put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(MESSAGE, String.format(InfoMessageConstants.LDAP_SERVER_TEST_SUCCEEDED, ldapConnection.getConnectedAddress())));
                            }

                            else
                            {
                                asyncResult.complete(event.put(STATUS, STATUS_FAIL).put(MESSAGE, String.format(ErrorMessageConstants.LDAP_SERVER_TEST_FAILED, result.result().get(MESSAGE)))
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LDAP_CONFIG_TEST).put(ERROR, result.result().get(ERROR)));

                            }

                            LDAPUtil.close(ldapConnection);
                        }

                        else
                        {

                            asyncResult.complete(event.put(STATUS, STATUS_FAIL).put(MESSAGE, String.format(ErrorMessageConstants.LDAP_SERVER_TEST_FAILED, result.cause().getMessage()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                        }
                    });

                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                asyncResult.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(ErrorMessageConstants.LDAP_SERVER_TEST_FAILED, exception.getMessage())).put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
            }

            asyncResult.future().onComplete(result -> notify(result.result(), EventBusConstants.UI_ACTION_CONFIGURATION_TEST));
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST, message ->
        {

            var event = message.body();

            var eventId = CommonUtil.newEventId();

            var future = Promise.<JsonObject>promise();

            vertx.eventBus().send(EVENT_ADD, new JsonObject().put(EVENT_CONTEXT, event)
                    .put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST)
                    .put(USER_NAME, event.getString(USER_NAME))
                    .put(EVENT_ID, eventId));
            try
            {

                var configs = new MailConfig().setTrustAll(true).setHostname(event.getString(MailServerConfiguration.MAIL_SERVER_HOST))
                        .setPort(event.getInteger(MailServerConfiguration.MAIL_SERVER_PORT));

                if (event.getValue(ProxyServer.PROXY_ENABLED) != null
                        && event.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES)
                        && WebClientUtil.getProxyOptions() != null)
                {
                    configs.setProxyOptions(WebClientUtil.getProxyOptions());
                }

                var mailMessage = new MailMessage();

                configs.setConnectTimeout((event.containsKey(TIMEOUT) ? event.getInteger(TIMEOUT) : 50) * 1000);// 60 sec connection timeout

                if (CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE != event.getLong(MailServerConfiguration.MAIL_SERVER_CREDENTIAL_PROFILE))
                {
                    var item = CredentialProfileConfigStore.getStore().getItem(event.getLong(MailServerConfiguration.MAIL_SERVER_CREDENTIAL_PROFILE));

                    configs.setUsername(event.getString(MailServerConfiguration.MAIL_SERVER_USERNAME));

                    Bootstrap.vertx().eventBus().request(EVENT_OAUTH_TOKEN_GENERATE, item,
                            new DeliveryOptions().setSendTimeout(15 * 1000L),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    configs.setPassword(result.result().body().toString());

                                    configs.setAuthMethods(OAuthUtil.AUTH_METHOD_OAUTH);

                                    buildMessage(event, configs, mailMessage);

                                    sendEmail(event, configs, mailMessage, future);
                                }
                                else
                                {
                                    future.fail(result.cause());

                                }
                            });
                }
                else
                {
                    if (event.containsKey(MailServerConfiguration.MAIL_SERVER_AUTH_STATUS) && event.getString(MailServerConfiguration.MAIL_SERVER_AUTH_STATUS).equalsIgnoreCase(YES))
                    {

                        configs.setUsername(event.getString(MailServerConfiguration.MAIL_SERVER_USERNAME)).setPassword(event.getString(MailServerConfiguration.MAIL_SERVER_PASSWORD));

                        configs.setLogin(LoginOption.REQUIRED);
                    }
                    else
                    {
                        configs.setLogin(LoginOption.DISABLED);
                    }

                    configs.setAuthMethods("LOGIN");

                    buildMessage(event, configs, mailMessage);

                    sendEmail(event, configs, mailMessage, future);
                }

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(MAIL_SERVER_TEST_FAILED, exception.getMessage())).put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));

            }

            future.future().onComplete(result ->
                    notify(result.result(), UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST));
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_LOG_FORWARDER_PROFILE_TEST, message ->
        {
            var event = message.body();

            var eventId = CommonUtil.newEventId();

            vertx.eventBus().send(EVENT_ADD, new JsonObject().put(EVENT_CONTEXT, event)
                    .put(EVENT_TYPE, UI_ACTION_LOG_FORWARDER_PROFILE_TEST)
                    .put(USER_NAME, event.getString(USER_NAME))
                    .put(EVENT_ID, eventId));

            try
            {
                if (event.containsKey(LogForwarder.LOG_FORWARDER_TYPE) && event.getString(LogForwarder.LOG_FORWARDER_TYPE).equalsIgnoreCase(LogForwarder.LOG_FORWARDER_TYPE_TCP))
                {
                    try
                    {
                        Bootstrap.vertx().createNetClient(new NetClientOptions().setReconnectAttempts(MotadataConfigUtil.getLogForwarderTCPRetries()).setReconnectInterval(MotadataConfigUtil.getLogForwarderTCPRetrySeconds() * 1000L))
                                .connect(CommonUtil.getInteger(event.getString(LogForwarder.LOG_FORWARDER_DESTINATION_PORT)), event.getString(LogForwarder.LOG_FORWARDER_DESTINATION_IP))
                                .onComplete(result ->
                                {

                                    if (result.succeeded())
                                    {
                                        LOGGER.info("TCP Connection established successfully...");

                                        event.put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(MESSAGE, InfoMessageConstants.LOG_FORWARDER_CONNECTION_ESTABLISHED_SUCCEEDED);

                                        notify(event, UI_ACTION_LOG_FORWARDER_PROFILE_TEST);

                                        result.result().close();
                                    }
                                    else
                                    {
                                        LOGGER.info("Failed to established TCP connection, " + result.cause().getMessage());

                                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_CONNECTION_FAILED).put(MESSAGE, String.format(LOG_FORWARDER_CONNECTION_FAILED, result.cause().getMessage())).put(ERROR, result.cause().getMessage());

                                        notify(event, UI_ACTION_LOG_FORWARDER_PROFILE_TEST);
                                    }
                                });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        event.put(STATUS, STATUS_FAIL)
                                .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage()));

                        notify(event, UI_ACTION_LOG_FORWARDER_PROFILE_TEST);
                    }

                }
                else if (event.containsKey(LogForwarder.LOG_FORWARDER_TYPE) && event.getString(LogForwarder.LOG_FORWARDER_TYPE).equalsIgnoreCase(LogForwarder.LOG_FORWARDER_TYPE_UDP))
                {

                    if (PingUtil.isReachable(event.getString(LogForwarder.LOG_FORWARDER_DESTINATION_IP), PORT_TIMEOUT_SECONDS, event.getInteger(PING_CHECK_RETRIES, 3), eventId))
                    {
                        LOGGER.info("UDP Connection established successfully...");

                        event.put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(MESSAGE, InfoMessageConstants.LOG_FORWARDER_CONNECTION_ESTABLISHED_SUCCEEDED);

                        notify(event, UI_ACTION_LOG_FORWARDER_PROFILE_TEST);
                    }
                    else
                    {
                        LOGGER.info("Failed to established UDP connection, " + String.format(PING_FAILED, event.getString(LogForwarder.LOG_FORWARDER_DESTINATION_IP)));

                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_CONNECTION_FAILED).put(MESSAGE, String.format(LOG_FORWARDER_CONNECTION_FAILED, String.format(PING_FAILED, event.getString(LogForwarder.LOG_FORWARDER_DESTINATION_IP)))).put(ERROR, String.format(PING_FAILED, event.getString(LogForwarder.LOG_FORWARDER_DESTINATION_IP)));

                        notify(event, UI_ACTION_LOG_FORWARDER_PROFILE_TEST);
                    }

                }

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                event.put(STATUS, STATUS_FAIL)
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage()));

                notify(event, UI_ACTION_LOG_FORWARDER_PROFILE_TEST);

            }

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST, message ->
        {

            var event = message.body();

            var future = Promise.<JsonObject>promise();

            var eventId = CommonUtil.newEventId();

            vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST)
                    .put(USER_NAME, event.getString(USER_NAME))
                    .put(EventBusConstants.EVENT_ID, eventId));

            try
            {
                var webClient = event.getValue(ProxyServer.PROXY_ENABLED) != null && event.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES) && WebClientUtil.getProxyOptions() != null ? WebClientUtil.getProxyWebClient() : WebClientUtil.getWebClient();

                webClient.getAbs(event.getString(SMSGatewayConfiguration.SMS_SERVER_GATEWAY_URL)
                                .replace("$$number$$", URLEncoder.encode(event.getString(TARGET), StandardCharsets.UTF_8))
                                .replace("$$message$$", URLEncoder.encode(event.getString(MESSAGE), StandardCharsets.UTF_8)))
                        .timeout(120 * 1000L) // milli sec
                        .send(result ->
                        {
                            if (result.succeeded())
                            {
                                if (result.result().statusCode() == HttpStatus.SC_OK) //success response code...
                                {
                                    var output = result.result().bodyAsString().toLowerCase();

                                    if (Notification.SMS_GATEWAY_ERROR_CODES.stream().anyMatch(output::contains))
                                    {
                                        future.complete(event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SMS_CONFIG_TEST).put(STATUS, STATUS_FAIL).put(MESSAGE, String.format(ErrorMessageConstants.SMS_GATEWAY_TEST_FAILED, result.result().bodyAsString())));
                                    }
                                    else
                                    {
                                        future.complete(event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(STATUS, STATUS_SUCCEED).put(MESSAGE, InfoMessageConstants.SMS_GATEWAY_TEST_SUCCEEDED));
                                    }
                                }
                                else
                                {
                                    future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_RESPONSE).put(MESSAGE, String.format(ErrorMessageConstants.SMS_GATEWAY_TEST_FAILED, result.result().bodyAsString())));
                                }
                            }
                            else
                            {
                                LOGGER.error(result.cause());

                                future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_RESPONSE).put(MESSAGE, String.format(ErrorMessageConstants.SMS_GATEWAY_TEST_FAILED, result.cause().getMessage())).put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                            }
                        });
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(ErrorMessageConstants.SMS_GATEWAY_TEST_FAILED, exception.getMessage())).put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
            }

            future.future().onComplete(result -> notify(result.result(), EventBusConstants.UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST));

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_PROXY_SERVER_TEST, message ->
        {

            var event = message.body();

            var future = Promise.<JsonObject>promise();

            var eventId = CommonUtil.newEventId();

            event.put(EventBusConstants.EVENT_ID, eventId);

            vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_ACTION_PROXY_SERVER_TEST)
                    .put(USER_NAME, event.getString(USER_NAME))
                    .put(EventBusConstants.EVENT_ID, eventId));

            var proxyOptions = new ProxyOptions().setHost(event.getString(ProxyServer.PROXY_SERVER_HOST)).setPort(event.getInteger(ProxyServer.PROXY_SERVER_PORT));

            if (event.getString(ProxyServer.PROXY_SERVER_USERNAME) != null && event.getString(ProxyServer.PROXY_SERVER_PASSWORD) != null)
            {
                proxyOptions.setUsername(event.getString(ProxyServer.PROXY_SERVER_USERNAME)).setPassword(event.getString(ProxyServer.PROXY_SERVER_PASSWORD));
            }

            var webClient = WebClient.create(vertx, new WebClientOptions().setTrustAll(true).setVerifyHost(false).setProxyOptions(proxyOptions));

            try
            {
                webClient.getAbs(event.getString(TARGET))
                        .timeout(event.containsKey(ProxyServer.PROXY_SERVER_TIME_OUT) ? event.getInteger(ProxyServer.PROXY_SERVER_TIME_OUT) * 1000 : 60000)
                        .send(result ->
                        {
                            if (result.succeeded())
                            {
                                if (result.result().statusCode() == HttpStatus.SC_OK) //success response code...
                                {
                                    future.complete(event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(STATUS, STATUS_SUCCEED).put(MESSAGE, InfoMessageConstants.PROXY_SERVER_TEST_SUCCEEDED));
                                }

                                else
                                {
                                    future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_RESPONSE).put(MESSAGE, String.format(ErrorMessageConstants.PROXY_SERVER_TEST_FAILED, result.result().bodyAsString())));
                                }
                            }

                            else
                            {
                                LOGGER.error(result.cause());

                                future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_RESPONSE).put(MESSAGE, String.format(ErrorMessageConstants.PROXY_SERVER_TEST_FAILED, result.cause().getMessage())).put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                            }
                        });
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(PROXY_SERVER_TEST_FAILED, exception.getMessage()))
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));

            }

            future.future().onComplete(result ->
            {

                webClient.close();

                notify(result.result(), EventBusConstants.UI_ACTION_PROXY_SERVER_TEST);
            });

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_TWO_FACTOR_AUTHENTICATION_TEST, message ->
        {

            var event = message.body();

            var future = Promise.<JsonObject>promise();

            var eventId = CommonUtil.newEventId();

            event.put(EventBusConstants.EVENT_ID, eventId);

            vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                    .put(EventBusConstants.EVENT_TYPE, UI_ACTION_TWO_FACTOR_AUTHENTICATION_TEST)
                    .put(USER_NAME, event.getString(USER_NAME))
                    .put(EventBusConstants.EVENT_ID, eventId));

            try
            {
                if (MailServerConfigStore.getStore().isConfigured())
                {
                    var user = UserConfigStore.getStore().getItemByValue(USER_NAME, event.getString(USER_NAME));

                    if (user != null && user.containsKey(USER_EMAIL) && !user.getString(USER_EMAIL).trim().isEmpty())
                    {
                        if (event.getString(REQUEST_PARAM_TYPE).equalsIgnoreCase(REQUEST_CREATE))
                        {
                            var otp = TwoFactorAuthenticationUtil.generateOTP();

                            ActiveUserCacheStore.getStore().updateItem(user.getString(User.USER_EMAIL), new JsonObject().put(OTP, otp).put("expiry.time", System.currentTimeMillis()));

                            Notification.sendEmail(new JsonObject()
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("security-lock.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                    .put(Notification.EMAIL_NOTIFICATION_SUBJECT, InfoMessageConstants.OTP_MAIL_SUBJECT)
                                    .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray().add(user.getString(User.USER_EMAIL)))
                                    .put(Notification.TEMPLATE_NAME, Notification.EMAIL_USER_OTP_VERIFICATION_HTML_TEMPLATE)
                                    .put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().put(USER_NAME, user.getString(USER_NAME)).put(OTP, CommonUtil.getString(otp))));

                            future.complete(event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(STATUS, STATUS_SUCCEED).put(MESSAGE, InfoMessageConstants.TWO_FACTOR_AUTHENTICATION_OTP_SENT_SUCCEEDED));
                        }
                        else
                        {
                            var item = ActiveUserCacheStore.getStore().getItem(user.getString(User.USER_EMAIL));

                            if (item != null && (item.containsKey("expiry.time") && (System.currentTimeMillis() - item.getLong("expiry.time")) < TimeUnit.MINUTES.toMillis(3)))
                            {
                                if (Objects.equals(item.getInteger(OTP), event.getInteger(OTP))) // if otp is valid
                                {
                                    ActiveUserCacheStore.getStore().deleteItem(user.getString(User.USER_EMAIL));

                                    future.complete(event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(STATUS, STATUS_SUCCEED).put(MESSAGE, EMAIL_ID_VALIDATED));
                                }
                                else
                                {
                                    future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_RESPONSE).put(MESSAGE, OTP_INVALID));
                                }
                            }
                            else
                            {
                                ActiveUserCacheStore.getStore().deleteItem(user.getString(User.USER_EMAIL));

                                future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_RESPONSE).put(MESSAGE, OTP_EXPIRED));
                            }

                        }
                    }
                    else
                    {
                        future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_RESPONSE).put(MESSAGE, USER_EMAIL_NOT_SET));
                    }

                }
                else
                {
                    future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(MESSAGE, MAIL_SEVER_NOT_CONFIGURED));
                }

            }

            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, exception.getMessage())
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));

            }

            future.future().onComplete(result ->
                    notify(result.result(), UI_ACTION_TWO_FACTOR_AUTHENTICATION_TEST));

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_CREDENTIAL_PROFILE_TEST, message ->
        {

            var event = message.body();

            var eventId = CommonUtil.newEventId();

            event.put(EventBusConstants.EVENT_ID, eventId);

            try
            {
                vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_ACTION_CREDENTIAL_PROFILE_TEST)
                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                        .put(USER_NAME, event.getString(USER_NAME))
                        .put(EventBusConstants.EVENT_ID, eventId));

                event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_ACTION_CREDENTIAL_PROFILE_TEST);

                var credentialProfile = new JsonObject();

                var valid = true;


                if (event.getString(REQUEST_PARAM_TYPE).equalsIgnoreCase(REQUEST_CREATE))
                {
                    credentialProfile.mergeIn(event.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT)).put(CredentialProfile.CREDENTIAL_PROFILE_NAME, event.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME))
                            .put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, event.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL));
                }

                else
                {
                    if (CredentialProfileConfigStore.getStore().existItem(event.getLong(ID)))
                    {
                        var credential = CredentialProfileConfigStore.getStore().getItem(event.getLong(ID));

                        if (credential != null && !credential.isEmpty())
                        {
                            credential.mergeIn(credential.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                            credentialProfile.mergeIn(credential);
                        }
                    }

                    else
                    {
                        valid = false;

                        event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.CREDENTIAL_PROFILE_TEST_FAILED_NOT_FOUND);

                        notify(event, EventBusConstants.UI_ACTION_CREDENTIAL_PROFILE_TEST);
                    }
                }

                if (valid)
                {
                    if (event.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                    {
                        credentialProfile.mergeIn(event.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                        credentialProfile.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                    }

                    event.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);

                    event.put(AIOpsObject.OBJECT_IP, event.getString(TARGET));

                    var status = new AtomicBoolean(false);

                    var protocol = NMSConstants.Protocol.valueOfName(event.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL));

                    var done = false;

                    if (protocol == NMSConstants.Protocol.SSH || protocol == NMSConstants.Protocol.TELNET)
                    {
                        event.put(PORT, credentialProfile.getInteger(PORT, protocol == NMSConstants.Protocol.TELNET ? TELNET_PORT : SSH_PORT));

                        if (credentialProfile.containsKey(NMSConstants.CLI_ENABLED) && credentialProfile.getString(NMSConstants.CLI_ENABLED).equals(YES))
                        {
                            event.put(PluginEngineConstants.PLUGIN_ENGINE, PluginEngineConstants.PluginEngine.GO.getName());

                            event.put(AIOpsObject.OBJECT_TARGET, event.getString(AIOpsObject.OBJECT_IP));

                            event.put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.getName());

                            status.set(true);
                        }
                        else
                        {
                            event.put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LINUX.getName());
                        }
                    }
                    else if (protocol == NMSConstants.Protocol.POWERSHELL)
                    {
                        event.put(PORT, 5985);

                        event.put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS.getName());
                    }
                    else if (protocol == Protocol.HTTP_HTTPS && AuthenticationType.OAUTH.getName().equalsIgnoreCase(credentialProfile.getString(OAuthUtil.AUTHENTICATION_TYPE)) && OAuthGrantType.AUTHORIZATION_CODE.getName().equalsIgnoreCase(credentialProfile.getString(OAuthUtil.GRANT_TYPE)))
                    {
                        var clientId = credentialProfile.getString(OAuthUtil.CLIENT_ID);

                        var authURL = credentialProfile.getString(OAuthUtil.AUTHENTICATION_URL);

                        var redirectURL = credentialProfile.getString(OAuthUtil.REDIRECT_URL);

                        var scopes = credentialProfile.getJsonArray("scopes");

                        credentialProfile.mergeIn(event);

                        // putting session id + event type in state of oauth
                        event.put("redirect.url", String.format(authURL + "?client_id=%s&response_type=code&redirect_uri=%s&scope=%s&state=%s&access_type=offline&prompt=consent", clientId, redirectURL, StringUtils.join(scopes.getList(), StringUtils.SPACE), new JsonObject().put(SESSION_ID, event.getString(SESSION_ID)).put(EVENT_TYPE, EventBusConstants.UI_ACTION_CREDENTIAL_PROFILE_TEST).encode()));

                        event.put(STATUS, EVENT_STATE_RUNNING).put(MESSAGE, InfoMessageConstants.OAUTH_REQUESTING_ACCESS_TOKEN_MESSAGE);

                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OAUTH_CONTEXT_UPDATE, credentialProfile);

                        notify(event, UI_ACTION_CREDENTIAL_PROFILE_TEST);

                        done = true;
                    }
                    else
                    {
                        event.put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SNMP_DEVICE.getName());

                        event.put(PORT, 161);

                        status.set(true);
                    }

                    if (!done)
                    {
                        if (!event.containsKey(PluginEngineConstants.PLUGIN_ENGINE))
                        {
                            event.put(PluginEngineConstants.PLUGIN_ENGINE, PluginEngineConstants.getPluginEngine(event.getString(Metric.METRIC_PLUGIN)));
                        }

                        event.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray(new ArrayList(1)).add(credentialProfile));

                        var pluginEngine = event.getString(PluginEngineConstants.PLUGIN_ENGINE) != null
                                ? PluginEngineConstants.PluginEngine.valueOfName(event.getString(PluginEngineConstants.PLUGIN_ENGINE)) : PluginEngineConstants.PluginEngine.PYTHON;

                        vertx.eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                .put(EventBusConstants.EVENT_CONTEXT, event));

                        vertx.<JsonObject>executeBlocking(future ->
                        {
                            try
                            {
                                var qualified = true;

                                EventBusConstants.startEvent(eventId, Thread.currentThread().getName());

                                if (credentialProfile.containsKey(NMSConstants.CLI_ENABLED) && credentialProfile.getString(NMSConstants.CLI_ENABLED).equals(YES)) // config credential test
                                {
                                    qualified = false;

                                    event.mergeIn(credentialProfile).put(Configuration.CONFIG_WORK_LOGS, new JsonObject())
                                            .put(EVENT, UI_ACTION_CREDENTIAL_PROFILE_TEST)
                                            .put(EventBusConstants.EVENT_ID, eventId)
                                            .put(PluginEngineConstants.PLUGIN_ENGINE_REQUEST, PluginEngineConstants.PluginEngineRequest.CONFIG.getName())
                                            .put(ConfigConstants.CONFIG_OPERATION, ConfigConstants.ConfigOperation.TEST.getName())
                                            .put(EventBusConstants.EVENT_TIMESTAMP, System.currentTimeMillis())
                                            .put(TIMEOUT, 60)
                                            .put(EventBusConstants.EVENT_REPLY, YES);

                                    if (PingUtil.isReachable(event.getString(AIOpsObject.OBJECT_IP), PORT_TIMEOUT_SECONDS, event.getInteger(NMSConstants.PING_CHECK_RETRIES, 3), eventId))
                                    {
                                        if (PortUtil.isConnected(event.getString(AIOpsObject.OBJECT_IP), event.getInteger(PORT, ConfigConstants.SSH_PORT), PORT_TIMEOUT_SECONDS))
                                        {
                                            qualified = true;
                                        }
                                        else
                                        {
                                            event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PORT).put(MESSAGE, String.format(ErrorMessageConstants.PORT_CONNECTION_FAILED, event.getInteger(PORT, ConfigConstants.SSH_PORT))).put(ERROR, event.getString(MESSAGE));

                                            future.complete(event);
                                        }
                                    }
                                    else
                                    {
                                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_PING_FAILED).put(MESSAGE, String.format(ErrorMessageConstants.PING_FAILED, event.getString(AIOpsObject.OBJECT_IP))).put(ERROR, event.getString(MESSAGE));

                                        future.complete(event);
                                    }
                                }

                                if (!status.get()) //means ssh/powershell credential test
                                {
                                    status.set(PortUtil.isConnected(event.getString(TARGET), event.getInteger(PORT)));

                                    if (status.get())
                                    {
                                        publish(event.getString(SESSION_ID), event.getString(EventBusConstants.EVENT_TYPE),
                                                event.put(MESSAGE, String.format(InfoMessageConstants.CREDENTIAL_PROFILE_TEST_PORT_CONNECTED, event.getInteger(PORT))));
                                    }

                                    else
                                    {
                                        future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PORT).put(MESSAGE, String.format(ErrorMessageConstants.PORT_CONNECTION_FAILED, event.getInteger(PORT))));

                                    }

                                    EventBusConstants.updateEvent(eventId, String.format(InfoMessageConstants.EVENT_TRACKER_PORT_CHECKED, DateTimeUtil.timestamp()));

                                }

                                if (status.get() && qualified)
                                {
                                    event.remove(MESSAGE);

                                    if (event.containsKey(NMSConstants.CLI_ENABLED) && event.getString(NMSConstants.CLI_ENABLED).equals(YES))
                                    {
                                        event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_PLUGIN_ENGINE);
                                    }
                                    else
                                    {
                                        event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DISCOVERY);
                                    }

                                    if (ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, event.getString(AIOpsObject.OBJECT_IP)) != null)
                                    {
                                        event.put(AIOpsObject.OBJECT_CATEGORY, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, event.getString(AIOpsObject.OBJECT_IP)).getString(AIOpsObject.OBJECT_CATEGORY));
                                    }

                                    var eventIds = new ArrayList<Long>(1);

                                    eventIds.add(eventId);

                                    var contexts = new HashMap<Long, JsonObject>();

                                    contexts.put(eventId, event);

                                    var result = WorkerUtil.spawnWorker(contexts, event, eventIds,
                                            event.getInteger(GlobalConstants.TIMEOUT, 60), false, pluginEngine, System.currentTimeMillis(), false);

                                    var errorCode = NMSConstants.getErrorCode(result);

                                    var errorMessage = errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? PROCESS_TIMED_OUT : INTERNAL_ERROR;

                                    if (result.containsKey(RESULT) && !result.getString(RESULT).isEmpty())
                                    {
                                        for (var line : result.getString(RESULT).split(VALUE_SEPARATOR_WITH_ESCAPE))
                                        {
                                            result.clear().mergeIn(new JsonObject(new String(Base64.getDecoder().decode(line.trim().replace(VALUE_SEPARATOR, EMPTY_VALUE)))));

                                            extractError(result);

                                            if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                            {
                                                future.complete(result.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS)
                                                        .put(MESSAGE, InfoMessageConstants.CREDENTIAL_PROFILE_TEST_SUCCEEDED));
                                            }
                                            else
                                            {
                                                extractErrorCode(result, CREDENTIAL_PROFILE_TEST_FAILED, ErrorCodes.ERROR_CODE_CREDENTIAL_TEST, null, null);

                                                future.complete(result.put(STATUS, STATUS_FAIL));
                                            }
                                        }
                                    }

                                    else
                                    {
                                        future.complete(event.put(STATUS, errorMessage.equalsIgnoreCase(PROCESS_TIMED_OUT) ? STATUS_TIME_OUT : STATUS_FAIL).put(ERROR_CODE, errorMessage.equalsIgnoreCase(PROCESS_TIMED_OUT) ? ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT : ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                .put(MESSAGE, String.format(ErrorMessageConstants.CREDENTIAL_PROFILE_TEST_FAILED, errorMessage)));
                                    }
                                }

                            }

                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                if (exception.getMessage() != null && exception.getMessage().contains(PROCESS_TIMED_OUT))
                                {
                                    future.complete(event.put(STATUS, STATUS_TIME_OUT).put(ERROR_CODE, ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT).put(MESSAGE, PROCESS_TIMED_OUT));

                                }
                                else
                                {
                                    future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(MESSAGE, String.format(ErrorMessageConstants.CREDENTIAL_PROFILE_TEST_FAILED, exception.getMessage()))
                                            .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
                                }


                            }

                        }, result -> notify(result.result(), EventBusConstants.UI_ACTION_CREDENTIAL_PROFILE_TEST));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                event.put(STATUS, STATUS_FAIL)
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage()));

                notify(event, EventBusConstants.UI_ACTION_CREDENTIAL_PROFILE_TEST);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_OID_GROUP_TEST, message ->
        {
            var event = message.body();

            var eventId = CommonUtil.newEventId();

            event.put(EventBusConstants.EVENT_ID, eventId);

            var future = Promise.<JsonObject>promise();

            try
            {
                var id = event.getLong(ID);

                event.put(NMSConstants.SNMP_OID_GROUP_ID, CommonUtil.getString(UUID.randomUUID()))
                        .put(Metric.METRIC_PLUGIN, event.getString(NMSConstants.SNMP_OID_GROUP_TYPE).equalsIgnoreCase(NMSConstants.OIDGroupType.SCALAR.getName()) ? NMSConstants.MetricPlugin.SNMP_SCALAR_METRIC.getName() : NMSConstants.MetricPlugin.SNMP_TABULAR_METRIC.getName());

                event.mergeIn(ObjectConfigStore.getStore().getItem(id));

                if (event.containsKey(AIOpsObject.OBJECT_CONTEXT))
                {
                    event.mergeIn(event.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                    event.remove(AIOpsObject.OBJECT_CONTEXT);
                }

                vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_ACTION_OID_GROUP_TEST)
                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                        .put(USER_NAME, event.getString(USER_NAME))
                        .put(EventBusConstants.EVENT_ID, eventId));

                var status = ObjectStatusCacheStore.getStore().getItem(id);

                if (event.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) && (status == null || status.equalsIgnoreCase(STATUS_UP)))
                {
                    var metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, NMSConstants.getMetricPlugin(NMSConstants.Type.SNMP_DEVICE)));

                    if (metric != null)
                    {
                        var credentialProfile = CredentialProfileConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE));

                        credentialProfile.mergeIn(credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                        credentialProfile.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);

                        // for timeout key as now poller queue event based on timeout only
                        if (metric.containsKey(Metric.METRIC_CONTEXT))
                        {
                            event.mergeIn(metric.getJsonObject(Metric.METRIC_CONTEXT));
                        }

                        event.mergeIn(credentialProfile);
                    }

                    event.put(EventBusConstants.EVENT_ID, eventId).put(EventBusConstants.EVENT, EventBusConstants.UI_ACTION_OID_GROUP_TEST)
                            .put(Metric.METRIC_CATEGORY, event.getString(AIOpsObject.OBJECT_CATEGORY))
                            .put(Metric.METRIC_TYPE, event.getString(AIOpsObject.OBJECT_TYPE))
                            .put(ID, CommonUtil.newId()) // to handle metric poller flow and to avoid duplicate event every time pass new dummy id to metric poller as we test with metric poller
                            .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_METRIC_POLL).put(EventBusConstants.EVENT_REPLY, YES);

                    vertx.eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                            .put(EventBusConstants.EVENT_CONTEXT, event));

                    if (!event.containsKey(TIMEOUT))
                    {
                        event.put(TIMEOUT, 60);
                    }

                    vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_ROUTER, event, DELIVERY_OPTIONS, reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                event.mergeIn(reply.result().body());

                                extractError(event);

                                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                {
                                    if (event.containsKey(Metric.METRIC_PLUGIN) &&
                                            event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_SCALAR_METRIC.getName()))
                                    {
                                        var results = new JsonArray();

                                        if (event.getJsonObject(RESULT) != null)
                                        {
                                            event.getJsonObject(RESULT).remove(SNMP_OID_GROUP_VALID_OIDS);

                                            var invalidOIDs = event.getJsonObject(RESULT).getJsonArray(SNMP_OID_GROUP_INVALID_OIDS);

                                            if (invalidOIDs != null)
                                            {
                                                event.getJsonObject(RESULT).remove(SNMP_OID_GROUP_INVALID_OIDS);

                                                var errors = new JsonObject();

                                                if (event.getJsonArray(ERRORS) != null)
                                                {
                                                    for (var index = 0; index < event.getJsonArray(ERRORS).size(); index++)
                                                    {
                                                        if (event.getJsonArray(ERRORS).getJsonObject(index).containsKey(SNMPOIDGroup.OID))
                                                        {
                                                            errors.put(event.getJsonArray(ERRORS).getJsonObject(index).getString(SNMPOIDGroup.OID),
                                                                    event.getJsonArray(ERRORS).getJsonObject(index).getString(MESSAGE));
                                                        }
                                                    }
                                                }

                                                for (var invalidOID : invalidOIDs)
                                                {
                                                    if (errors.containsKey(CommonUtil.getString(invalidOID)))
                                                    {
                                                        results.add(new JsonObject().put(SNMPOIDGroup.OID, invalidOID).put(STATUS, STATUS_FAIL)
                                                                .put(MESSAGE, errors.getString(CommonUtil.getString(invalidOID))));
                                                    }
                                                }
                                            }
                                            event.getJsonObject(RESULT).getMap().forEach((key, value) -> results.add(new JsonObject().put(SNMPOIDGroup.OID, key)
                                                    .put(GlobalConstants.VALUE, value).put(STATUS, STATUS_SUCCEED)));
                                        }
                                        event.put(RESULT, results);
                                    }

                                    future.complete(event.put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(MESSAGE, InfoMessageConstants.SNMP_OID_GROUP_TEST_SUCCEEDED)
                                            .put(EventBusConstants.EVENT_ID, eventId));
                                }
                                else
                                {
                                    extractErrorCode(event, ErrorMessageConstants.SNMP_OID_GROUP_TEST_FAILED, ErrorCodes.ERROR_CODE_SNMP_OID_GROUP_TEST, null, null);

                                    future.complete(event.put(STATUS, STATUS_FAIL).put(EventBusConstants.EVENT_ID, eventId));
                                }
                            }
                            else
                            {
                                future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(ERROR, CommonUtil.formatStackTrace(reply.cause().getStackTrace()))
                                        .put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(MESSAGE, String.format(SNMP_OID_GROUP_TEST_FAILED, reply.cause().getMessage())).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)))
                                        .put(EventBusConstants.EVENT_ID, eventId)
                                        .put(MESSAGE, String.format(ErrorMessageConstants.SNMP_OID_GROUP_TEST_FAILED, reply.cause().getMessage())));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }
                else
                {
                    event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(MESSAGE, String.format(ErrorMessageConstants.SNMP_OID_GROUP_TEST_FAILED, ErrorMessageConstants.OBJECT_ERROR));

                    future.complete(event);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.complete(event.put(STATUS, STATUS_FAIL)
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
            }

            // event complete send to event tracker from response process -> metric.poll
            future.future().onComplete(result -> notify(result.result(), EventBusConstants.UI_ACTION_OID_GROUP_TEST));
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_TASK_STREAM_START, message -> vertx.eventBus().send(EventBusConstants.EVENT_STREAM_START, message.body())).exceptionHandler(LOGGER::error);

//            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_TASK_STREAM_EXPORT, message -> vertx.eventBus().send(EVENT_STREAM_EXPORT, message.body())).exceptionHandler(logger::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_TASK_STREAM_STOP, message -> vertx.eventBus().send(EventBusConstants.EVENT_STREAM_STOP, message.body())).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_TASK_STREAM_CONTEXT, message -> vertx.eventBus().send(EVENT_STREAM_CONTEXT, message.body())).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_TOPOLOGY_HIERARCHY_FETCH, message ->
        {
            var event = message.body();

            var future = Promise.<JsonObject>promise();

            try
            {
                event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_ACTION_TOPOLOGY_HIERARCHY_FETCH)
                        .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName());

                vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY_QUERY, event, DELIVERY_OPTIONS, reply ->
                {
                    if (reply.succeeded())
                    {
                        future.complete(reply.result().body());
                    }
                    else
                    {
                        future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR, CommonUtil.formatStackTrace(reply.cause().getStackTrace()))
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR));
                    }
                });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.complete(event.put(STATUS, STATUS_FAIL)
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
            }

            future.future().onComplete(result -> EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_ACTION_TOPOLOGY_HIERARCHY_FETCH, future.future().result()));

        }).exceptionHandler(LOGGER::error);

        /*
         * This event gives current flap data of policy. which will be appended to actual DB response to show current flap data in Alert Drill-Down.
         */
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_POLICY_ACTIVE_FLAP_GET, message ->
        {
            var event = message.body();

            var future = Promise.<JsonObject>promise();

            var key = event.getLong(ENTITY_ID) + SEPARATOR + event.getLong(PolicyEngineConstants.POLICY_ID) + SEPARATOR + (event.getValue(INSTANCE, null) != null && !CommonUtil.getString(event.getValue(INSTANCE)).isEmpty() ? event.getString(GlobalConstants.METRIC) + SEPARATOR + event.getValue(INSTANCE) : event.getString(GlobalConstants.METRIC));

            try
            {
                Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY, new JsonObject().put(PolicyEngineConstants.POLICY_KEY, key), reply ->
                {
                    try
                    {
                        var duration = reply.result().body();

                        if (duration != null && !duration.isEmpty())
                        {
                            future.complete(new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESULT, duration));
                        }
                        else
                        {
                            future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR, "No record(s) found.")
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        future.fail(exception);
                    }
                });

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.complete(event.put(STATUS, STATUS_FAIL)
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
            }

            future.future().onComplete(asyncResult ->
            {
                if (asyncResult.succeeded())
                {
                    var result = asyncResult.result();

                    var response = new JsonObject();

                    if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        var value = result.getJsonObject(RESULT);

                        DateTimeUtil.buildTimeline(event, new JsonObject(), UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME)));

                        var timeLine = event.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE);

                        // need to check if the current time is greater than the previous flap time
                        if (timeLine.getLong(VisualizationConstants.TO_DATETIME) >= (value.getLong(PREVIOUS_FLAP_TIMESTAMP) * 1000))
                        {

                            var timeDifference = (timeLine.getLong(VisualizationConstants.TO_DATETIME) / 1000) - (timeLine.getLong(VisualizationConstants.FROM_DATETIME) / 1000);

                            // need to append "DOT_SEPARATOR + VALUE" in each column to match result with DB response, so UI can easily merge cache result with DB response
                            response.put(AIOpsObject.OBJECT_ID + DOT_SEPARATOR + VALUE, CommonUtil.getString(ObjectConfigStore.getStore().getItem(value.getLong(APIConstants.ENTITY_ID), false).getInteger(AIOpsObject.OBJECT_ID)))
                                    .put(INSTANCE + DOT_SEPARATOR + VALUE, value.containsKey(INSTANCE) && !value.getString(INSTANCE).isEmpty() ? CommonUtil.getString(value.getValue(INSTANCE)) : EMPTY_VALUE)
                                    .put(PolicyEngineConstants.POLICY_ID + DOT_SEPARATOR + VALUE, CommonUtil.getString(value.getLong(ID)))
                                    .put(DURATION + DOT_SEPARATOR + VALUE, CommonUtil.getString((timeDifference > value.getInteger(DURATION)) ? value.getInteger(DURATION) : timeDifference))     // in case of Smaller timeline we need to adjust the duration according to the timeline
                                    .put(SEVERITY + DOT_SEPARATOR + VALUE, value.getString(SEVERITY))
                                    .put(MetricPolicy.POLICY_THRESHOLD + DOT_SEPARATOR + VALUE, CommonUtil.getString(value.getValue(MetricPolicy.POLICY_THRESHOLD, EMPTY_VALUE)))
                                    .put(VALUE + DOT_SEPARATOR + VALUE, CommonUtil.getString(value.getValue(PREVIOUS_FLAP_VALUE, value.getValue(VALUE))))
                                    .put(METRIC + DOT_SEPARATOR + VALUE, CommonUtil.getString(value.getValue(METRIC)))
                                    .put(TIME_STAMP, (value.getLong(PREVIOUS_FLAP_TIMESTAMP) * 1000))   // need to convert timestamp into miliseconds for UI as DB is storing in miliseconds
                                    .put(PolicyEngineConstants.POLICY_TYPE + DOT_SEPARATOR + VALUE, CommonUtil.getString(value.getValue(PolicyEngineConstants.POLICY_TYPE)));
                        }
                    }

                    EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_ACTION_POLICY_ACTIVE_FLAP_GET, event.put(RESULT, response));

                }
                else
                {
                    EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_ACTION_POLICY_ACTIVE_FLAP_GET, event.put(RESULT, new JsonObject()));
                }

            });

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_TOPOLOGY_PARENT_FETCH, message ->
        {
            var event = message.body();

            try
            {
                var category = event.getString(AIOpsObject.OBJECT_CATEGORY);

                if (category.equalsIgnoreCase(NMSConstants.Category.NETWORK.getName()))
                {
                    vertx.eventBus().<JsonObject>request(EVENT_PARENT_CHILD_HIERARCHY_FETCH, new JsonObject(), DELIVERY_OPTIONS, reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                event.put(AIOpsConstants.DEPENDENCY_PARENT, reply.result().body()).put(STATUS, STATUS_SUCCEED);
                            }
                            else
                            {
                                event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, reply.cause().getMessage());
                            }
                        }
                        catch (Exception exception)
                        {
                            event.put(STATUS, STATUS_FAIL)
                                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage()));

                            LOGGER.error(exception);
                        }
                        finally
                        {
                            publish(event.getString(SESSION_ID), UI_ACTION_TOPOLOGY_PARENT_FETCH, event);
                        }
                    });
                }
                else
                {
                    var hierarchy = new AtomicBoolean(true);

                    if (category.equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()))
                    {
                        event.put(AIOpsConstants.DEPENDENCY_PARENT, ObjectConfigStore.getStore().flatItemsByValues(AIOpsObject.OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.AWS_CLOUD.getName()).add(NMSConstants.Type.AZURE_CLOUD.getName()), ID));
                    }
                    else if (category.equalsIgnoreCase(NMSConstants.Category.VIRTUALIZATION.getName()))
                    {
                        event.put(AIOpsConstants.DEPENDENCY_PARENT, ObjectConfigStore.getStore().flatItemsByValues(AIOpsObject.OBJECT_TYPE, new JsonArray(NMSConstants.VIRTUALIZATION_TYPES.stream().map(CommonUtil::getString).collect(Collectors.toList())), ID));
                    }
                    else if (category.equalsIgnoreCase(Category.HCI.getName()))
                    {
                        event.put(AIOpsConstants.DEPENDENCY_PARENT, ObjectConfigStore.getStore().flatItemsByValues(AIOpsObject.OBJECT_TYPE, new JsonArray(HCI_TYPES.stream().map(CommonUtil::getString).collect(Collectors.toList())), ID));
                    }
                    else if (category.equalsIgnoreCase(Category.SDN.getName()))
                    {
                        event.put(AIOpsConstants.DEPENDENCY_PARENT, ObjectConfigStore.getStore().flatItemsByValues(AIOpsObject.OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.CISCO_VMANAGE.getName()).add(NMSConstants.Type.CISCO_VEDGE.getName()).add(Type.CISCO_ACI.getName()), ID));
                    }
                    else if (category.equalsIgnoreCase(NMSConstants.Category.OTHER.getName()))
                    {
                        hierarchy.set(false);

                        event.put(AIOpsConstants.DEPENDENCY_PARENT, ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.CISCO_UCS.getName(), ID));
                    }

                    if (event.getJsonArray(AIOpsConstants.DEPENDENCY_PARENT) != null)
                    {
                        var parents = new JsonArray();

                        var futures = new ArrayList<Future<Void>>();

                        for (var parent : event.getJsonArray(AIOpsConstants.DEPENDENCY_PARENT))
                        {
                            var future = Promise.<Void>promise();

                            futures.add(future.future());

                            vertx.eventBus().<JsonObject>request(EVENT_DEPENDENCY_QUERY,
                                    new JsonObject().put(AIOpsConstants.ENTITY_ID, CommonUtil.getLong(parent)).put(AIOpsObject.OBJECT_CATEGORY, category)
                                            .put(EVENT_REPLY, YES).put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                            .put(AIOpsConstants.RECURSIVE_DEPENDENCIES, NO)
                                            .put(AIOpsConstants.DEPENDENCY_FORMAT, hierarchy.get() ? AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName() : AIOpsConstants.DependencyFormat.FLAT.getName()),
                                    DELIVERY_OPTIONS,
                                    reply ->
                                    {
                                        try
                                        {
                                            if (reply.succeeded())
                                            {
                                                var qualified = true;

                                                var context = reply.result().body();

                                                if (hierarchy.get())
                                                {
                                                    var result = context.getJsonObject(RESULT);

                                                    if (result != null && (result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN) == null || result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty()))
                                                    {
                                                        qualified = false;
                                                    }
                                                }
                                                else
                                                {
                                                    var valid = false;

                                                    var items = context.getJsonArray(RESULT);

                                                    if (items != null)
                                                    {
                                                        for (var index = 0; index < items.size(); index++)
                                                        {
                                                            var item = items.getJsonObject(index);

                                                            if (item.containsKey(AIOpsConstants.DEPENDENCY_DESTINATION))
                                                            {
                                                                valid = true;

                                                                break;
                                                            }
                                                        }
                                                    }

                                                    if (!valid)
                                                    {
                                                        qualified = false;
                                                    }
                                                }

                                                if (qualified)
                                                {
                                                    parents.add(parent);
                                                }
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);
                                        }

                                        future.complete();
                                    });
                        }

                        Future.join(futures).onComplete(result -> publish(event.getString(SESSION_ID), UI_ACTION_TOPOLOGY_PARENT_FETCH, event.put(AIOpsConstants.DEPENDENCY_PARENT, parents)));
                    }
                    else
                    {
                        publish(event.getString(SESSION_ID), UI_ACTION_TOPOLOGY_PARENT_FETCH, event.put(AIOpsConstants.DEPENDENCY_PARENT, new JsonArray(new ArrayList<Long>(1))));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_METRIC_PLUGIN_TEST, message ->
        {
            var event = message.body();

            var entities = new JsonArray();

            var pluginContext = event.containsKey(MetricPlugin.METRIC_PLUGIN_CONTEXT) ? event.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT) : null;

            if (event.getString(MetricPlugin.METRIC_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()) || event.containsKey(ID))
            {
                entities.add(event.getLong(ID));
            }
            else
            {
                entities.addAll(event.getJsonArray(MetricPlugin.METRIC_PLUGIN_ENTITIES));
            }

            for (var index = 0; index < entities.size(); index++)
            {
                var context = event.copy();

                var eventId = CommonUtil.newEventId();

                context.put(EventBusConstants.EVENT_ID, eventId);

                try
                {
                    var object = ObjectConfigStore.getStore().getItem(entities.getLong(index));

                    if (object.getJsonObject(AIOpsObject.OBJECT_CONTEXT) != null)
                    {
                        object.mergeIn(object.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                        object.remove(AIOpsObject.OBJECT_CONTEXT);
                    }

                    vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, context)
                            .put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_ACTION_METRIC_PLUGIN_TEST)
                            .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                            .put(USER_NAME, context.getString(USER_NAME))
                            .put(EventBusConstants.EVENT_ID, eventId));

                    var status = ObjectStatusCacheStore.getStore().getItem(object.getLong(ID));

                    context.mergeIn(object);

                    if (context.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) && (status == null || status.equalsIgnoreCase(STATUS_UP)))
                    {
                        if (pluginContext != null && pluginContext.containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
                        {
                            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, pluginContext.getLong(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE));
                        }

                        context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_METRIC_POLL);

                        enrich(context, MetricPlugin.METRIC_PLUGIN_TYPE, MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, context.getString(AIOpsObject.OBJECT_TYPE));

                        if (pluginContext != null)
                        {
                            context.mergeIn(pluginContext);

                            context.remove(MetricPlugin.METRIC_PLUGIN_CONTEXT);
                        }

                        context.put(TIMEOUT, context.getLong(TIMEOUT, 60L));

                        if (context.getString(NMSConstants.URL_ENDPOINT) != null)
                        {
                            context.put(AIOpsObject.OBJECT_TARGET, context.getString(AIOpsObject.OBJECT_TARGET) + context.getString(NMSConstants.URL_ENDPOINT));
                        }

                        context.put(ID, CommonUtil.newId()) // to continue metric poller flow will generate new id every time so it will not throw duplicate context error
                                .put(EventBusConstants.EVENT, EventBusConstants.UI_ACTION_METRIC_PLUGIN_TEST)
                                .put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.valueOfName(!context.getString(MetricPlugin.METRIC_PLUGIN_PROTOCOL).equalsIgnoreCase(TopologyPluginType.SNMP.getName()) ? context.getString(MetricPlugin.METRIC_PLUGIN_PROTOCOL) : NMSConstants.MetricPlugin.SNMP_METRIC.getName()).getName());

                        vertx.eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                .put(EventBusConstants.EVENT_CONTEXT, context));

                        EventBusConstants.startEvent(eventId, Thread.currentThread().getName());

                        vertx.eventBus().send(EventBusConstants.EVENT_ROUTER, context);
                    }
                    else
                    {
                        context.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(MESSAGE, String.format(ErrorMessageConstants.METRIC_PLUGIN_TEST_FAILED, ErrorMessageConstants.OBJECT_ERROR));

                        notify(context, EventBusConstants.UI_ACTION_METRIC_PLUGIN_TEST);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    context.put(STATUS, STATUS_FAIL)
                            .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage()));

                    notify(context, EventBusConstants.UI_ACTION_METRIC_PLUGIN_TEST);
                }
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_RUNBOOK_PLUGIN_TEST, message ->
        {
            var event = message.body();

            try
            {
                var entities = new JsonArray();

                if (event.containsKey(ID)) // in case of monitor/group runbook
                {
                    entities.add(event.getLong(ID));
                }
                else if (event.containsKey(EVENT_SOURCE)) // in case of ip/host runbook
                {
                    entities.add(event.getString(EVENT_SOURCE));
                }
                else
                {
                    entities.addAll(event.getJsonArray(RUNBOOK_PLUGIN_ENTITIES));
                }

                for (var index = 0; index < entities.size(); index++)
                {
                    var context = event.copy();

                    var eventId = CommonUtil.newEventId();

                    context.put(EventBusConstants.EVENT_ID, eventId);

                    context.put(RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(entities.getValue(index)));

                    vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, context)
                            .put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_ACTION_RUNBOOK_PLUGIN_TEST)
                            .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                            .put(USER_NAME, context.getString(USER_NAME))
                            .put(EventBusConstants.EVENT_ID, eventId));

                    context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_RUNBOOK);

                    if (context.getString(RUNBOOK_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(EVENT_SOURCE))
                    {
                        context.put(AIOpsObject.OBJECT_DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.REMOTE.name());
                    }

                    var pluginContext = context.getJsonObject(RUNBOOK_PLUGIN_CONTEXT);

                    // if event contains timeout then use the same timout for both timeout and request timeout else set default timeout
                    if (pluginContext.getValue(TIMEOUT) != null)
                    {
                        pluginContext.put(REQUEST_TIMEOUT, (pluginContext.getLong(TIMEOUT) * entities.size()) + 1); // multiply the timeout using batch size to support test runbook on multiple entities
                    }
                    else
                    {
                        pluginContext.put(TIMEOUT, 60L);
                    }

                    context.put(EventBusConstants.EVENT, EventBusConstants.UI_ACTION_RUNBOOK_PLUGIN_TEST).put(EventBusConstants.EVENT_REPLY, YES);

                    // need to put the object entity type as in case of entity.type=group the testing of runbook is executing on all the monitors present inside that group
                    if (context.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_ENTITY_TYPE) && context.getString(RunbookPlugin.RUNBOOK_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
                    {
                        context.put(RUNBOOK_PLUGIN_ENTITY_TYPE, Entity.OBJECT.getName());
                    }

                    vertx.eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                            .put(EventBusConstants.EVENT_CONTEXT, context));

                    vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_RUNBOOK, context,
                            pluginContext.containsKey(REQUEST_TIMEOUT) && pluginContext.getLong(REQUEST_TIMEOUT) > 0 ? new DeliveryOptions().setSendTimeout(TimeUnit.SECONDS.toMillis(pluginContext.getLong(REQUEST_TIMEOUT))) : DELIVERY_OPTIONS, reply ->
                            {
                                try
                                {
                                    if (reply.succeeded())
                                    {
                                        context.mergeIn(reply.result().body());

                                        var replyContexts = context.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS);

                                        if (replyContexts != null && !replyContexts.isEmpty())
                                        {
                                            context.mergeIn(CommonUtil.removeSensitiveFields(replyContexts.getJsonObject(0), true));

                                            extractError(context);

                                            if (context.containsKey(STATUS) && context.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                            {
                                                context.put(MESSAGE, InfoMessageConstants.RUNBOOK_PLUGIN_TEST_SUCCEEDED);
                                            }
                                            else
                                            {
                                                context.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_RUNBOOK_PLUGIN_TEST);
                                            }
                                        }
                                        else
                                        {
                                            context.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(RUNBOOK_PLUGIN_FAILED, "reply contexts not found"));
                                        }
                                    }
                                    else
                                    {
                                        context.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(RUNBOOK_PLUGIN_FAILED, reply.cause().getMessage()))
                                                .put(ERROR, CommonUtil.formatStackTrace(reply.cause().getStackTrace()));
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }

                                if (CommonUtil.debugEnabled())
                                {
                                    LOGGER.debug("Runbook was executed for " + context.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME) + " status : " + context.getString(STATUS) + " Message : " + context.getString(MESSAGE));
                                }

                                notify(context, EventBusConstants.UI_ACTION_RUNBOOK_PLUGIN_TEST);
                            });
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                event.put(STATUS, STATUS_FAIL)
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage()));

                notify(event, EventBusConstants.UI_ACTION_RUNBOOK_PLUGIN_TEST);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_TOPOLOGY_PLUGIN_TEST, message ->
        {
            var event = message.body();

            var eventId = CommonUtil.newEventId();

            event.put(EventBusConstants.EVENT_ID, eventId);

            try
            {
                var object = ObjectConfigStore.getStore().getItem(event.getLong(ID));

                if (object.getJsonObject(AIOpsObject.OBJECT_CONTEXT) != null)
                {
                    object.mergeIn(object.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                    object.remove(AIOpsObject.OBJECT_CONTEXT);
                }

                vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_ACTION_TOPOLOGY_PLUGIN_TEST)
                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                        .put(USER_NAME, event.getString(USER_NAME))
                        .put(EventBusConstants.EVENT_ID, eventId));

                var status = ObjectStatusCacheStore.getStore().getItem(object.getLong(ID));

                event.mergeIn(object);

                if (event.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) && (status == null || status.equalsIgnoreCase(STATUS_UP)))
                {
                    if (event.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT))
                    {
                        event.mergeIn(event.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT));

                        event.remove(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);
                    }

                    event.put(Metric.METRIC_DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.valueOf(object.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD)) == NMSConstants.DiscoveryMethod.AGENT
                                    ? NMSConstants.DiscoveryMethod.AGENT.name() : NMSConstants.DiscoveryMethod.REMOTE.name())
                            .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_TOPOLOGY);

                    enrich(event, Metric.METRIC_TYPE, TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE, NMSConstants.Type.SNMP_DEVICE.getName());

                    event.put(ID, object.getLong(ID))
                            .put(EventBusConstants.EVENT, EventBusConstants.UI_ACTION_TOPOLOGY_PLUGIN_TEST).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_TOPOLOGY).put(EventBusConstants.EVENT_REPLY, YES);

                    var pluginEngine = PluginEngineConstants.getPluginEngine(event);

                    if (pluginEngine != null)
                    {
                        event.put(PluginEngineConstants.PLUGIN_ENGINE, pluginEngine);
                    }

                    vertx.eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                            .put(EventBusConstants.EVENT_CONTEXT, event));

                    EventBusConstants.startEvent(eventId, Thread.currentThread().getName());

                    vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_ROUTER, event, DELIVERY_OPTIONS, reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                event.mergeIn(reply.result().body());

                                extractError(event);

                                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                {
                                    event.put(MESSAGE, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED);
                                }
                                else
                                {
                                    extractErrorCode(event, TOPOLOGY_PLUGIN_TEST_FAILED, ErrorCodes.ERROR_CODE_TOPOLOGY_PLUGIN_TEST, null, null);
                                }
                            }
                            else
                            {
                                event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(TOPOLOGY_PLUGIN_TEST_FAILED, reply.cause().getMessage()))
                                        .put(ERROR, CommonUtil.formatStackTrace(reply.cause().getStackTrace()));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                        notify(event, EventBusConstants.UI_ACTION_TOPOLOGY_PLUGIN_TEST);
                    });
                }
                else
                {
                    event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(MESSAGE, String.format(ErrorMessageConstants.TOPOLOGY_PLUGIN_TEST_FAILED, ErrorMessageConstants.OBJECT_ERROR));

                    notify(event, EventBusConstants.UI_ACTION_TOPOLOGY_PLUGIN_TEST);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                event.put(STATUS, STATUS_FAIL)
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage()));

                notify(event, EventBusConstants.UI_ACTION_TOPOLOGY_PLUGIN_TEST);
            }

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_RUNBOOK_PLUGIN_RUN, message ->
        {
            try
            {
                var event = message.body();

                if (event.getValue(RUNBOOK_PLUGIN_INSTANCES) != null && !event.getJsonArray(RUNBOOK_PLUGIN_INSTANCES).isEmpty())
                {
                    var instances = event.getJsonArray(RUNBOOK_PLUGIN_INSTANCES);

                    for (var index = 0; index < instances.size(); index++)
                    {
                        vertx.eventBus().send(EventBusConstants.EVENT_RUNBOOK, event.copy().put(INSTANCE, instances.getString(index)));
                    }
                }
                else
                {
                    vertx.eventBus().send(EventBusConstants.EVENT_RUNBOOK, event);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_COMPLIANCE_POLICY_RUN, message -> vertx.eventBus().send(EVENT_COMPLIANCE_MANAGER, message.body())).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_STREAMING_START, message -> vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_START, message.body())).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_STREAMING_STOP, message -> vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_STOP, message.body())).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_SCHEDULER_RUN, message ->
        {
            var event = message.body();

            try
            {
                JobScheduler.triggerJob(SchedulerConfigStore.getStore().getItem(event.getLong(ID))
                        .put(SESSION_ID, event.getString(SESSION_ID))
                        .put(USER_NAME, event.getString(USER_NAME)));
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_REDISCOVER, message ->
        {
            try
            {
                var event = message.body();

                switch (RediscoverJob.valueOfName(event.getString(REDISCOVER_JOB)))
                {
                    case WAN_LINK ->
                    {
                        if (event.containsKey(OBJECT_VENDOR) && event.getString(OBJECT_VENDOR).equalsIgnoreCase(CISCO_VENDOR))
                        {
                            var errorMessage = EMPTY_VALUE;

                            var object = ObjectConfigStore.getStore().getItem(event.getLong(ID));

                            if (object != null && object.getString(OBJECT_STATE).equalsIgnoreCase(State.ENABLE.name())
                                    && (ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)) == null
                                    || ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)).equalsIgnoreCase(STATUS_UP)))
                            {
                                var credential = event.containsKey(Discovery.DISCOVERY_CREDENTIAL_PROFILES) ? CredentialProfileConfigStore.getStore().getItem(event.getLong(Discovery.DISCOVERY_CREDENTIAL_PROFILES)) : null;

                                // check if SNMP write community is available as we need that for SNMP set operations
                                if (credential != null && !credential.isEmpty()
                                        && (credential.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL).equalsIgnoreCase(Protocol.SNMPV3.getName())
                                        || credential.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).containsKey("snmp.write.community")))
                                {
                            /* why need this logic? Because we need to check provisioned instances operation wise.
                             So we are fetching all provisioned instances & as we are dumping provisioned instances into common plugin i.e. ipsla, we will fetch only from it.
                             */
                                    var provisionedInstances = MetricConfigStore.getStore().getInstancesByPlugins(object.getLong(ID), JsonArray.of(IPSLA));

                                    var objects = new JsonArray();

                                    var valid = true;

                                    var keyBuilder = new StringBuilder();

                                    credential.mergeIn(credential.getJsonObject(CREDENTIAL_PROFILE_CONTEXT));

                                    credential.remove(CREDENTIAL_PROFILE_CONTEXT);

                                    event.mergeIn(credential);

                                    if (object.getJsonObject(OBJECT_CONTEXT) != null)
                                    {
                                        object.mergeIn(object.getJsonObject(OBJECT_CONTEXT));

                                        object.remove(OBJECT_CONTEXT);
                                    }

                                    if (CommonUtil.getString(event.remove(Discovery.DISCOVERY_OBJECT_TYPE)).equalsIgnoreCase(Discovery.DISCOVERY_TYPE_CSV))
                                    {
                                        var csvFile = CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + CommonUtil.getString(event.remove(Discovery.DISCOVERY_TARGET));

                                        if (Bootstrap.vertx().fileSystem().existsBlocking(csvFile))
                                        {
                                            var content = CommonUtil.getString(Bootstrap.vertx().fileSystem().readFileBlocking(csvFile));

                                            if (CommonUtil.isNotNullOrEmpty(content))
                                            {
                                                var columns = content.trim().split("\\n");

                                                for (var index = 0; index < columns.length; index++)
                                                {
                                                    if (index > 0 && columns[index].trim().split(COMMA_SEPARATOR).length >= 6)
                                                    {
                                                        var context = new JsonObject();

                                                        var values = columns[index].trim().split(COMMA_SEPARATOR);

                                                        keyBuilder.setLength(0);

                                                        if (CommonUtil.isNotNullOrEmpty(values[0]) && NMSConstants.MetricPlugin.valueOfName(values[0]) != null)
                                                        {
                                                            context.put(OBJECT_TYPE, values[0]);
                                                        }
                                                        else
                                                        {
                                                            errorMessage = String.format(REDISCOVER_FAILED, "provided WAN probe type is not valid");
                                                        }

                                                        context.put(SOURCE_IP, object.getString(OBJECT_IP));

                                                        keyBuilder.append(object.getString(OBJECT_IP)).append(ARROW_SEPARATOR);

                                                        if (CommonUtil.isNotNullOrEmpty(values[1]))
                                                        {
                                                            // check if provided interface IP is present & provisioned.
                                                            var item = MACScannerConfigStore.getStore().getItemByValue(MACScanner.MAC_SCANNER_INTERFACE_IP_ADDRESS, values[1]);

                                                            if (item != null)
                                                            {
                                                                context.put(SOURCE_IP, values[1]);

                                                                context.put(SOURCE_INTERFACE_NAME, item.getString(MACScanner.MAC_SCANNER_INTERFACE));

                                                                keyBuilder.setLength(0);

                                                                keyBuilder.append(values[1]).append(ARROW_SEPARATOR);
                                                            }
                                                            else
                                                            {
                                                                valid = false;

                                                                errorMessage = String.format(REDISCOVER_FAILED, "provided interface ip address is not found");
                                                            }
                                                        }

                                                        if (CommonUtil.isNotNullOrEmpty(values[2]))
                                                        {
                                                            context.put(SOURCE_ROUTER_LOCATION, values[2]);
                                                        }

                                                        if (CommonUtil.isNotNullOrEmpty(values[3]) && (PATTERN_IP_ADDRESS.matcher(values[3]).find() || PATTERN_IPV6_ADDRESS.matcher(values[3]).find()))
                                                        {
                                                            context.put(DESTINATION_IP, values[3]);

                                                            keyBuilder.append(values[3]);
                                                        }
                                                        else
                                                        {
                                                            valid = false;

                                                            errorMessage = String.format(REDISCOVER_FAILED, "provided ip address is not valid");
                                                        }

                                                        if (CommonUtil.isNotNullOrEmpty(values[4]))
                                                        {
                                                            context.put(DESTINATION_ROUTER_LOCATION, values[4]);
                                                        }

                                                        if (CommonUtil.isNotNullOrEmpty(values[5]))
                                                        {
                                                            context.put(INTERNET_SERVICE_PROVIDER, values[5]);

                                                            keyBuilder.insert(0, values[0] + DASH_SEPARATOR + values[5] + DASH_SEPARATOR);

                                                        }
                                                        else
                                                        {
                                                            valid = false;

                                                            errorMessage = String.format(REDISCOVER_FAILED, "provided internet service provider is not valid");
                                                        }

                                                        if (valid)
                                                        {
                                                    /* Check if specified instance is not already provisioned, if already provisioned then it won't be qualified for rediscovery.
                                                       Duplicate provision check will be based on unique key combination i.e. "Operation_Type-ISP-Source_IP->Destination_IP".
                                                     */
                                                            if (!provisionedInstances.containsKey(IPSLA) || !provisionedInstances.getJsonArray(IPSLA).contains(keyBuilder.toString()))
                                                            {
                                                                objects.add(context);
                                                            }
                                                            else
                                                            {
                                                                errorMessage = String.format(REDISCOVER_FAILED, "WAN Link is already provisioned");
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        var context = event.getJsonObject(OBJECT).put(OBJECT_TYPE, event.getString(OBJECT_TYPE));

                                        keyBuilder.append(context.getString(OBJECT_TYPE)).append(DASH_SEPARATOR).append(context.getString(INTERNET_SERVICE_PROVIDER)).append(DASH_SEPARATOR);

                                        context.put(SOURCE_IP, object.getString(OBJECT_IP));

                                        if (context.containsKey(INTERFACE_IP))
                                        {
                                            context.put(SOURCE_IP, context.getString(INTERFACE_IP));

                                            context.put(SOURCE_INTERFACE_NAME, context.getString(INTERFACE_NAME));

                                            keyBuilder.append(context.getString(SOURCE_IP)).append(ARROW_SEPARATOR);
                                        }
                                        else
                                        {
                                            keyBuilder.append(object.getString(OBJECT_IP)).append(ARROW_SEPARATOR);
                                        }

                                        keyBuilder.append(context.getString(DESTINATION_IP));

                                        // check if specified instance is not already provisioned
                                        if (!provisionedInstances.containsKey(IPSLA) || !provisionedInstances.getJsonArray(IPSLA).contains(keyBuilder.toString()))
                                        {
                                            objects.add(new JsonObject().mergeIn(context));

                                            event.remove(OBJECT);

                                            event.remove(OBJECT_TYPE);
                                        }
                                        else
                                        {
                                            errorMessage = String.format(REDISCOVER_FAILED, "WAN Link is already provisioned");
                                        }
                                    }

                                    if (!objects.isEmpty()) // means qualified objects are there for rediscovery
                                    {
                                        event.mergeIn(object);

                                        if (CommonUtil.traceEnabled())
                                        {
                                            LOGGER.trace(String.format("qualified objects for IPSLA rediscovery %s", objects.encode()));
                                        }

                                        event.put(OBJECTS, objects)
                                                .put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IPSLA.getName())
                                                .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER);

                                        publish(event.getString(SESSION_ID), UI_ACTION_REDISCOVER_START, new JsonObject().put(ID, event.getLong(ID)).put(REDISCOVER_JOB, event.getString(REDISCOVER_JOB)).put(MESSAGE, String.format(InfoMessageConstants.WAN_LINK_CONFIGURATION_INITIALIZED, object.getString(OBJECT_NAME))));

                                        vertx.eventBus().send(EVENT_ROUTER, event.put(EVENT_TYPE, EVENT_REDISCOVER)
                                                .put(EVENT_ID, CommonUtil.newEventId())
                                                .put(TIMEOUT, event.containsKey(TIMEOUT) ? event.getInteger(TIMEOUT) : 120)
                                                .put(OBJECT_EVENT_PROCESSORS, new JsonArray()
                                                        .add(RemoteEventProcessorConfigStore.getStore().flatItemsByValue(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name(), REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode()).getJsonObject(0).getLong(ID))));
                                    }
                                    else
                                    {
                                        publish(event.getString(SESSION_ID), UI_NOTIFICATION_REDISCOVER_PROGRESS,
                                                event.put(STATUS, STATUS_FAIL).put(MESSAGE, errorMessage).put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER));
                                    }
                                }
                                else
                                {
                                    publish(event.getString(SESSION_ID), UI_NOTIFICATION_REDISCOVER_PROGRESS, event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_CREDENTIAL_NOT_ASSIGNED)
                                            .put(MESSAGE, String.format(REDISCOVER_FAILED, "Either credential profile not found or write community is not provided")).put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER));
                                }
                            }
                            else
                            {
                                publish(event.getString(SESSION_ID), UI_NOTIFICATION_REDISCOVER_PROGRESS, event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                                        .put(MESSAGE, String.format(REDISCOVER_FAILED, OBJECT_ERROR)).put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER));
                            }
                        }
                    }
                    case CONTAINER ->
                    {
                        var item = ObjectConfigStore.getStore().getItem(event.getLong(ID));

                        if (item != null && item.getString(OBJECT_STATE).equalsIgnoreCase(State.ENABLE.name())
                                && (ObjectStatusCacheStore.getStore().getItem(item.getLong(ID)) == null
                                || ObjectStatusCacheStore.getStore().getItem(item.getLong(ID)).equalsIgnoreCase(STATUS_UP)))
                        {
                            var credential = event.containsKey(Discovery.DISCOVERY_CREDENTIAL_PROFILES) ? CredentialProfileConfigStore.getStore().getItem(event.getLong(Discovery.DISCOVERY_CREDENTIAL_PROFILES)) : null;

                            if (credential != null)
                            {
                                credential.mergeIn(credential.getJsonObject(CREDENTIAL_PROFILE_CONTEXT));

                                credential.remove(CREDENTIAL_PROFILE_CONTEXT);

                                event.put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.valueOfName(event.getString(Metric.METRIC_PLUGIN)).getName())
                                        .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                                        .put(Metric.METRIC_OBJECT, item.getLong(ID));

                                event.mergeIn(credential).mergeIn(item);

                                if (event.getJsonObject(OBJECT_CONTEXT) != null)
                                {
                                    event.mergeIn(item.getJsonObject(OBJECT_CONTEXT));

                                    event.remove(OBJECT_CONTEXT);
                                }

                                if (event.containsKey(DISCOVERY_CONTEXT))
                                {
                                    event.mergeIn(event.getJsonObject(DISCOVERY_CONTEXT));
                                }

                                publish(event.getString(SESSION_ID), UI_ACTION_REDISCOVER_START, new JsonObject().put(ID, event.getLong(ID)).put(REDISCOVER_JOB, event.getString(REDISCOVER_JOB)));

                                vertx.eventBus().send(EVENT_ROUTER, event.put(EVENT_TYPE, EVENT_REDISCOVER)
                                        .put(EVENT_ID, CommonUtil.newEventId())
                                        .put(TIMEOUT, event.containsKey(TIMEOUT) ? event.getInteger(TIMEOUT) : 120));
                            }
                            else
                            {
                                publish(event.getString(SESSION_ID), UI_NOTIFICATION_REDISCOVER_PROGRESS, event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_CREDENTIAL_NOT_ASSIGNED)
                                        .put(MESSAGE, String.format(REDISCOVER_FAILED, String.format(ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.CREDENTIAL_PROFILE.getName()))).put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER));
                            }
                        }
                        else
                        {
                            publish(event.getString(SESSION_ID), UI_NOTIFICATION_REDISCOVER_PROGRESS, event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                                    .put(MESSAGE, String.format(REDISCOVER_FAILED, OBJECT_ERROR)).put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER));
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_REDISCOVER_STOP, message -> vertx.eventBus().send(EventBusConstants.EVENT_REDISCOVER_STOP, message.body())).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_TOPOLOGY_STOP, message -> vertx.eventBus().send(EventBusConstants.EVENT_TOPOLOGY_STOP, message.body())).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_POLL, message ->
        {
            var event = message.body();

            try
            {
                if (ObjectConfigStore.getStore().existItem(event.getLong(ID)))
                {

                    if (ObjectConfigStore.getStore().getItem(event.getLong(ID)).getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                    {

                        vertx.eventBus().send(EventBusConstants.EVENT_METRIC_POLL_SCHEDULE, event.getLong(ID));

                        event.put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(USER_NAME, event.getString(USER_NAME))
                                .put(MESSAGE, InfoMessageConstants.POLL_EVENT_QUEUED);
                    }
                    else
                    {

                        //#25018
                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(USER_NAME, event.getString(USER_NAME))
                                .put(MESSAGE, String.format(OBJECT_STATE_IS_SAME_ERROR, StringUtils.capitalize(ObjectConfigStore.getStore().getItem(event.getLong(ID)).getString(AIOpsObject.OBJECT_STATE).toLowerCase())));

                    }
                }
                else
                {
                    event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(MESSAGE, ErrorMessageConstants.OBJECT_NOT_FOUND);
                }

                publish(event.getString(SESSION_ID), EventBusConstants.UI_ACTION_POLL, event);

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_DISCOVERY_RESULT_EXPORT, message ->
        {
            try
            {
                var event = message.body();

                var discoveryId = event.getJsonArray(APIConstants.REQUEST_PARAM_IDS).getLong(0);

                Bootstrap.configDBService().getAll(DBConstants.TBL_DISCOVERY_RESULT + discoveryId, result ->
                {
                    try (var writer = new CSVWriterBuilder(new FileWriter(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + event.getString(UI_EVENT_UUID).trim() + ".csv")).withSeparator(',').withLineEnd("\n").build())
                    {
                        //  header
                        writer.writeNext(new String[]{"IP Address", "Hostname", "Target", "Device Type", "Status", "Reason"}, true);

                        if (result.succeeded() && !result.result().isEmpty())
                        {
                            for (var index = 0; index < result.result().size(); index++)
                            {
                                var row = result.result().getJsonObject(index);

                                writer.writeNext(new String[]
                                        {
                                                row.getString(OBJECT_IP, EMPTY_VALUE),
                                                row.getString(OBJECT_HOST, EMPTY_VALUE),
                                                row.getString(OBJECT_TARGET, EMPTY_VALUE),
                                                row.getString(OBJECT_TYPE, EMPTY_VALUE),
                                                row.getString(OBJECT_STATE, EMPTY_VALUE),
                                                row.getString("message", EMPTY_VALUE),
                                        }, true);

                                writer.flushQuietly();
                            }

                            writer.flushQuietly();

                            EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_CSV_EXPORT_READY, new JsonObject().put(GlobalConstants.FILE_NAME, event.getString(UI_EVENT_UUID).trim() + ".csv").put(STATUS, STATUS_SUCCEED).put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID)).put(RESPONSE_CODE, HttpStatus.SC_OK));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }


                });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_VISUALIZATION_RENDER, message ->
        {
            var event = message.body();

            if (event.containsKey(REPORT_ID))
            {
                vertx.eventBus().send(EVENT_REPORT_RENDER, event);
            }
            else
            {
                // Adding preview limit while creating new reports from UI
                if (event.containsKey(VisualizationConstants.CONTAINER_TYPE) && event.getString(VisualizationConstants.CONTAINER_TYPE).equalsIgnoreCase("report"))
                {
                    event.put(MAX_RECORDS, ReportConstants.REPORT_PREVIEW_MAX_RECORDS);
                }

                vertx.eventBus().send(EVENT_VISUALIZATION, event);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_INSTANCE_SEVERITY_QUERY, message ->
        {
            var event = message.body();

            var result = new JsonObject();

            if (!event.getJsonArray(EVENT_CONTEXT).isEmpty())
            {
                var key = APPS;

                if (event.getBoolean(INSTANCE))
                {
                    key = INSTANCE;
                }

                var contexts = event.getJsonArray(EVENT_CONTEXT);

                for (var i = 0; i < contexts.size(); i++)
                {
                    var context = contexts.getJsonObject(i);

                    var severity = EMPTY_VALUE;

                    if (key.equalsIgnoreCase(INSTANCE))
                    {
                        severity = MetricPolicyCacheStore.getStore().getInstanceSeverity(context.getValue(ENTITY_ID) + SEPARATOR + context.getValue(INSTANCE));
                    }
                    else
                    {
                        severity = MetricPolicyCacheStore.getStore().getAppSeverity(context.getValue(ENTITY_ID) + SEPARATOR + context.getValue(INSTANCE));
                    }

                    result.put(context.getValue(ENTITY_ID) + SEPARATOR + context.getValue(INSTANCE), severity != null ? severity : Severity.UNKNOWN.name());
                }
            }

            if (!result.isEmpty())
            {
                EventBusConstants.publish(event.getString(SESSION_ID), EventBusConstants.UI_ACTION_INSTANCE_SEVERITY_QUERY, event.put(RESULT, result));
            }
            else
            {
                EventBusConstants.publish(event.getString(SESSION_ID), EventBusConstants.UI_ACTION_INSTANCE_SEVERITY_QUERY, event);
            }

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_VISUALIZATION_ABORT, message -> vertx.eventBus().publish(EVENT_VISUALIZATION_QUERY_ABORT, message.body().getLong(VisualizationConstants.QUERY_ID))).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_METRIC_POLL_TIMESTAMP_FETCH, message ->
        {
            var event = message.body();

            var timestamps = MetricCacheStore.getStore().getMetricPollTimestamps(event.getLong(ID));

            if (!timestamps.isEmpty())
            {
                var items = MetricConfigStore.getStore().getItemsByObject(event.getLong(ID));

                var type = EMPTY_VALUE;

                if (event.containsKey(APPLICATION))
                {
                    type = event.getString(APPLICATION);
                }
                else
                {
                    type = ObjectConfigStore.getStore().getItem(event.getLong(ID)).getString(AIOpsObject.OBJECT_TYPE);
                }

                // need to send separate result of application and monitor

                var result = new JsonObject();

                for (var item : items)
                {
                    if (item.getString(Metric.METRIC_TYPE).equalsIgnoreCase(type) || (NMSConstants.CUSTOM_METRIC_TYPES.contains(item.getString(Metric.METRIC_TYPE)) && item.containsKey(Metric.METRIC_CONTEXT) && item.getJsonObject(Metric.METRIC_CONTEXT).getString(MetricPlugin.METRIC_PLUGIN_TYPE).equalsIgnoreCase(type)))
                    {
                        result.put(item.getString(Metric.METRIC_NAME), timestamps.getLong(item.getString(Metric.METRIC_NAME)));
                    }
                }


                result.put(ID, event.getLong(ID));

                result.put(EVENT_TIMESTAMP, timestamps.getString(EVENT_TIMESTAMP));

                EventBusConstants.publish(event.getString(SESSION_ID), EventBusConstants.UI_ACTION_METRIC_POLL_TIMESTAMP_FETCH, event.put(RESULT, result));
            }
            else
            {
                EventBusConstants.publish(event.getString(SESSION_ID), EventBusConstants.UI_ACTION_METRIC_POLL_TIMESTAMP_FETCH, event);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_POLICY_CLEAR, message ->
        {
            var event = message.body();

            if (event.containsKey(NetRoute.NETROUTE_ID))
            {
                Bootstrap.vertx().eventBus().publish(EVENT_NETROUTE_POLICY_CLEAR, event.put(ENTITY_ID, event.getLong(NetRoute.NETROUTE_ID)));
            }
            else
            {
                Bootstrap.vertx().eventBus().publish(EVENT_METRIC_POLICY_CLEAR, event.put(ENTITY_ID, event.getLong(AIOpsObject.OBJECT_ID)));
            }

            vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_SUCCEED).put(EVENT_TYPE, EVENT_POLICY_ACTION)
                    .put(MESSAGE, String.format(InfoMessageConstants.POLICY_CLEARED, event.getLong(POLICY_ID) > 0 && event.containsKey(OBJECT_ID) ? MetricPolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID), false).getString(POLICY_NAME)
                            : NetRoutePolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID), false).getString(POLICY_NAME), event.getString(User.USER_NAME))));

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_POLICY_SUPPRESS, message ->
        {
            var event = message.body();

            var dateTime = DateTimeUtil.getScheduledTimestamp(System.currentTimeMillis() + (event.getLong(PolicyEngineConstants.POLICY_SUPPRESSION_TIME) * 1000L));

            var request = EVENT_EVENT_POLICY_SUPPRESS;

            var eventPolicy = new AtomicBoolean(true);

            if (event.containsKey(AIOpsObject.OBJECT_ID))
            {
                event.put(ENTITY_ID, event.remove(AIOpsObject.OBJECT_ID));

                request = EVENT_METRIC_POLICY_SUPPRESS;

                eventPolicy.set(false);
            }
            else if (event.containsKey(NetRoute.NETROUTE_ID))
            {
                event.put(ENTITY_ID, event.remove(NetRoute.NETROUTE_ID));

                request = EVENT_NETROUTE_POLICY_SUPPRESS;

                eventPolicy.set(false);
            }

            Bootstrap.vertx().eventBus().<Boolean>request(request, event, reply ->
            {
                if (reply.succeeded() && Boolean.FALSE.equals(reply.result().body()))
                {
                    event.put(Scheduler.SCHEDULER_TIMES, new JsonArray().add(dateTime.split(" ")[1])).put(Scheduler.SCHEDULER_START_DATE, dateTime.split(" ")[0]).put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.POLICY_SUPPRESSION.getName()).put(Scheduler.SCHEDULER_STATE, YES).put(Scheduler.SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE);

                    Bootstrap.configDBService().save(DBConstants.TBL_SCHEDULER, event, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            SchedulerConfigStore.getStore().addItem(asyncResult.result()).onComplete(result -> JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(asyncResult.result())));

                            vertx.eventBus().send(EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_SUCCEED).put(EVENT_TYPE, EVENT_POLICY_ACTION)
                                    .put(MESSAGE, String.format(InfoMessageConstants.POLICY_SUPPRESSED, eventPolicy.get()
                                            ? EventPolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID)).getString(PolicyEngineConstants.POLICY_NAME)
                                            : event.containsKey(NetRoute.NETROUTE_ID) ? NetRoutePolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID), false).getString(PolicyEngineConstants.POLICY_NAME)
                                            : MetricPolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID), false).getString(PolicyEngineConstants.POLICY_NAME), event.getString(USER_NAME), dateTime)));
                        }
                    });
                }
                else
                {
                    LOGGER.info(String.format("Policy %s is already suppressed...", eventPolicy.get() ? EventPolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID)).getString(PolicyEngineConstants.POLICY_NAME) : MetricPolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID)).getString(PolicyEngineConstants.POLICY_NAME)));
                }
            });

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_POLICY_ACKNOWLEDGE, message ->
        {
            var event = message.body();

            event.remove(SESSION_ID);

            event.remove("action.type");

            event.remove("alert.type");

            if (event.containsKey(AIOpsObject.OBJECT_ID))
            {
                vertx.eventBus().send(EVENT_METRIC_POLICY_ACKNOWLEDGE, event);
            }
            else if (event.containsKey(NetRoute.NETROUTE_ID))
            {
                vertx.eventBus().send(EVENT_NETROUTE_POLICY_ACKNOWLEDGE, event);
            }

            vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_SUCCEED).put(EVENT_TYPE, EVENT_POLICY_ACTION).put(MESSAGE, String.format(event.getString(ACKNOWLEDGED).equalsIgnoreCase(YES) ? InfoMessageConstants.POLICY_ACKNOWLEDGED : InfoMessageConstants.POLICY_UNACKNOWLEDGED, event.getLong(POLICY_ID) > 0
                    ? MetricPolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID), false).getString(PolicyEngineConstants.POLICY_NAME) : NetRoutePolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID), false).getString(PolicyEngineConstants.POLICY_NAME), event.getString(User.USER_NAME))));

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_POLICY_COMMENT_UPDATE, message ->
        {
            var event = message.body();

            if (event.containsKey(AIOpsObject.OBJECT_ID))
            {
                vertx.eventBus().send(EVENT_METRIC_POLICY_COMMENT_UPDATE, event);
            }
            else if (event.containsKey(NetRoute.NETROUTE_ID))
            {
                vertx.eventBus().send(EVENT_NETROUTE_POLICY_COMMENT_UPDATE, event);
            }

            vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_SUCCEED).put(EVENT_TYPE, EVENT_POLICY_ACTION).put(MESSAGE, String.format(InfoMessageConstants.POLICY_NOTED, event.getLong(POLICY_ID) > 0
                    ? MetricPolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID), false).getString(PolicyEngineConstants.POLICY_NAME) : NetRoutePolicyConfigStore.getStore().getItem(event.getLong(POLICY_ID), false).getString(PolicyEngineConstants.POLICY_NAME), event.getString(User.USER_NAME), event.getString(PolicyEngineConstants.POLICY_NOTE))));

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_TRAP_ACKNOWLEDGE, message ->
        {
            var event = message.body();

            var buffer = Buffer.buffer();

            buffer.appendByte(DatastoreConstants.OperationType.TRAP_ACKNOWLEDGE.getName()).appendBytes(event.put(EventBusConstants.EVENT_TYPE, DatastoreConstants.DatastoreType.TRAP.ordinal()).encode().getBytes());

            TrapCacheStore.getStore().updateItem(event.getString(EVENT_SOURCE) + DASH_SEPARATOR + event.getString(SNMP_TRAP_OID), event.getString(ACKNOWLEDGED));

            vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_SUCCEED).put(EVENT_TYPE, EVENT_TRAP_ACTION).put(MESSAGE, String.format(event.getString(ACKNOWLEDGED).equalsIgnoreCase(YES) ? InfoMessageConstants.TRAP_ACKNOWLEDGED : InfoMessageConstants.TRAP_UNACKNOWLEDGED, event.getString(SNMP_TRAP_OID) != null ? SNMPTrapProfileConfigStore.getStore().getTrapProfiles(event.getString(SNMP_TRAP_OID)).getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_NAME) : EMPTY_VALUE, event.getString(User.USER_NAME))));

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_LOG_PARSER_SAMPLE_FILE_READ, message ->
        {
            var event = message.body();

            try
            {
                var events = new JsonArray();

                if (event.containsKey(LogEngineConstants.SAMPLE_FILE_NAME))
                {
                    var logFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + CommonUtil.getString(event.getString(LogEngineConstants.SAMPLE_FILE_NAME));

                    if (Bootstrap.vertx().fileSystem().existsBlocking(logFile))
                    {
                        var validFile = new AtomicBoolean(Boolean.TRUE);

                        try (var lines = Files.lines(Paths.get(logFile)))
                        {
                            lines.limit(1000).forEach(line ->
                            {
                                if (line.getBytes().length > 1000000) //if log size greater than 1 MB need to ignore that file
                                {
                                    validFile.set(Boolean.FALSE);
                                }
                                else
                                {
                                    events.add(line.trim());
                                }
                            });
                        }

                        if (validFile.get())
                        {
                            event.put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(RESULT, events);
                        }
                        else
                        {
                            event.put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.LOG_FILE_SIZE_LIMIT_EXCEEDED);
                        }
                    }
                }

                publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_SAMPLE_FILE_READ, event.put(RESULT, events));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_SAMPLE_FILE_READ, event.put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(GlobalConstants.MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_LOG_PARSER_SAMPLE_LOG_PARSE, message ->
        {
            var event = message.body();

            event.put(LogEngineConstants.PROCESSOR_CATEGORY, LogEngineConstants.getCategory(LogEngineConstants.Type.valueOfName(event.getString(LogParser.LOG_PARSER_SOURCE_TYPE))).getName());

            vertx.eventBus().<JsonObject>request(EVENT_LOG_PARSING_PROBE, event)
                    .onComplete(result ->
                    {

                        if (result.succeeded())
                        {
                            publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_SAMPLE_LOG_PARSE, event.put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(RESULT, result.result().body()));
                        }
                        else
                        {
                            publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_SAMPLE_LOG_PARSE, event.put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(MESSAGE, result.cause().getMessage()));
                        }
                    });

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_LOG_PARSER_SAMPLE_FILE_UPLOAD, message ->
        {
            var event = message.body();

            try
            {
                if (event.containsKey(LogEngineConstants.SAMPLE_FILE_NAME))
                {
                    var logFile = CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + event.getString(LogEngineConstants.SAMPLE_FILE_NAME);

                    if (Bootstrap.vertx().fileSystem().existsBlocking(logFile))
                    {
                        try (var lines = Files.lines(Paths.get(logFile)))
                        {
                            lines.forEach(line ->
                            {
                                if (LicenseUtil.updateUsedLogQuota(line.getBytes().length))
                                {
                                    Bootstrap.vertx().eventBus().send(EVENT_LOG, new JsonObject()
                                            .put(EventBusConstants.EVENT_VOLUME_BYTES, line.getBytes().length)
                                            .put(EventBusConstants.EVENT, line.trim())
                                            .put(ID, event.getValue(ID))
                                            .put(EVENT_SOURCE, "127.0.0.1")
                                            .put(LogEngineConstants.RECEIVED_TIMESTAMP, DateTimeUtil.currentSeconds())
                                            .put(EVENT_TYPE, EVENT_LOG));
                                }
                            });
                        }
                    }
                }

                publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_SAMPLE_FILE_UPLOAD, event.put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(MESSAGE, "File Uploaded Successfully"));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_SAMPLE_FILE_UPLOAD, event.put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                        .put(STATUS, STATUS_FAIL)
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_LOG_PARSER_PATTERN_DETECT, message ->
        {
            var event = message.body();

            try
            {
                var log = event.getString(LOG_PARSER_EVENT);

                var positions = event.getJsonArray(LOG_PARSER_LOG_POSITIONS);

                if (event.getJsonArray(LogParser.LOG_PARSER_CONDITION_KEYWORDS).isEmpty() || event.getString(LogParser.LOG_PARSER_CONDITION).equals("all") ? event.getJsonArray(LogParser.LOG_PARSER_CONDITION_KEYWORDS).stream().map(item -> CommonUtil.getString(item).toLowerCase()).toList().stream().allMatch(log.toLowerCase()::contains) : event.getJsonArray(LogParser.LOG_PARSER_CONDITION_KEYWORDS).stream().map(item -> CommonUtil.getString(item).toLowerCase()).toList().stream().anyMatch(log.toLowerCase()::contains))
                {
                    if (event.getString(LogParser.LOG_PARSER_TYPE).equalsIgnoreCase(LogEngineConstants.LogParserType.REGEX.getName()))
                    {
                        var result = LogPatternDetector.buildPattern(log, positions);

                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("generated regex %s for log %s", result.getString("regex"), log));
                        }

                        if (!result.isEmpty())
                        {
                            publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_DETECT, event.put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(RESULT, result));
                        }
                        else
                        {
                            publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_DETECT, event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.LOG_PARSING_FAILED));
                        }
                    }
                    else if (event.getString(LogParser.LOG_PARSER_TYPE).equalsIgnoreCase(LogEngineConstants.LogParserType.CUSTOM.getName()))
                    {
                        var result = new JsonObject();

                        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_LOG_PARSER_PLUGIN_PARSE, event.put(EventBusConstants.EVENT, log), reply ->
                        {

                            if (reply.succeeded() && reply.result().body() != null && !reply.result().body().isEmpty())
                            {
                                var body = reply.result().body();

                                var fields = new JsonArray();

                                body.getMap().entrySet().stream().filter(item -> !item.getKey().equalsIgnoreCase(MESSAGE)).forEach(item ->
                                        fields.add(new JsonObject()
                                                .put(LogParserField.TYPE.getName(), item.getKey().equalsIgnoreCase(GlobalConstants.TIME_STAMP) ? GlobalConstants.TIME_STAMP : "none")
                                                .put(LogParserField.VALUE.getName(), item.getValue())
                                                .put(LogParserField.NAME.getName(), item.getKey())));

                                result.put(LOG_PARSER_FIELDS, fields);
                            }

                            if (!result.isEmpty())
                            {
                                publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_DETECT, event.put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(RESULT, result));
                            }
                            else
                            {
                                publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_DETECT, event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.LOG_PARSING_FAILED));
                            }
                        });
                    }
                    else
                    {
                        publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_DETECT, event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.LOG_PARSER_TYPE_NOT_AVAILABLE));
                    }
                }
                else
                {
                    publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_DETECT, event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.LOG_PARSING_FAILED));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_DETECT, event.put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(GlobalConstants.MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_LOG_PARSER_PATTERN_PARSE, message ->
        {
            var event = message.body();

            try
            {
                var log = event.getString(LOG_PARSER_EVENT);

                if (event.getJsonArray(LogParser.LOG_PARSER_CONDITION_KEYWORDS).isEmpty() || event.getString(LogParser.LOG_PARSER_CONDITION).equals("all") ? event.getJsonArray(LogParser.LOG_PARSER_CONDITION_KEYWORDS).stream().map(item -> CommonUtil.getString(item).toLowerCase()).toList().stream().allMatch(log.toLowerCase()::contains) : event.getJsonArray(LogParser.LOG_PARSER_CONDITION_KEYWORDS).stream().map(item -> CommonUtil.getString(item).toLowerCase()).toList().stream().anyMatch(log.toLowerCase()::contains))
                {
                    if (event.getString(LogParser.LOG_PARSER_TYPE).equalsIgnoreCase(LogEngineConstants.LogParserType.REGEX.getName()))
                    {
                        var result = new JsonObject().put(REGEX.getName(), event.getString(REGEX.getName()));

                        var pattern = Pattern.compile(event.getString(REGEX.getName()), Pattern.CASE_INSENSITIVE);

                        try
                        {
                            var matcher = pattern.matcher(log);

                            if (matcher.find())
                            {
                                var fields = new JsonArray();

                                var positions = new JsonArray();

                                if (matcher.groupCount() > 0)
                                {
                                    for (var index = 1; index <= matcher.groupCount(); index++)
                                    {
                                        if (matcher.group(index) != null)
                                        {
                                            positions.add(new JsonObject().put(START_POS, matcher.start(index)).put(END_POS, matcher.end(index)));

                                            fields.add(new JsonObject().put(LogParserField.TYPE.getName(), "none").put(LogParserField.VALUE.getName(), matcher.group(index)).put(LogParserField.NAME.getName(), "Field " + index));
                                        }
                                    }
                                }

                                result.put(LOG_PARSER_FIELDS, fields).put(LOG_PARSER_LOG_POSITIONS, positions);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("Result : %s for log : %s", result.encode(), log));
                        }

                        if (!result.isEmpty())
                        {
                            publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_PARSE, event.put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(RESULT, result));
                        }
                        else
                        {
                            publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_PARSE, event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.LOG_PARSING_FAILED));
                        }
                    }
                    else
                    {
                        publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_DETECT, event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.LOG_PARSER_TYPE_NOT_AVAILABLE));
                    }
                }
                else
                {
                    publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_PARSE, event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.LOG_PARSING_FAILED));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                publish(event.getString(SESSION_ID), UI_ACTION_LOG_PARSER_PATTERN_PARSE, event.put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(GlobalConstants.MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_SNMP_OID_SEARCH, message -> Bootstrap.vertx().<JsonObject>executeBlocking(future ->
        {

            var event = message.body();

            try (var directory = FSDirectory.open(Paths.get(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "indices")))
            {
                if (event.containsKey(FILTER))
                {
                    var result = new JsonObject();

                    var startTime = System.currentTimeMillis();

                    var queryContext = event.getJsonObject(FILTER);

                    var query = "+" + SNMPOIDGroup.VENDOR + ":(" + StringEscapeUtils.escapeJavaScript(queryContext.getString(SNMPOIDGroup.VENDOR).toLowerCase().trim()) + ") AND +" + SNMPOIDGroup.OID_NAME + ":(*" + queryContext.getString(SNMPOIDGroup.OID_NAME).toLowerCase().replaceAll(" ", "").trim() + "*)" + " AND +" + SNMPOIDGroup.OID_TYPE + ":" + queryContext.getString(SNMPOIDGroup.OID_TYPE).toLowerCase().trim();

                    if (queryContext.getString(SNMPOIDGroup.OID_TYPE).equalsIgnoreCase("tabular"))
                    {
                        query = "+" + SNMPOIDGroup.VENDOR + ":(" + StringEscapeUtils.escapeJavaScript(queryContext.getString(SNMPOIDGroup.VENDOR).toLowerCase().trim()) + ") AND ((+" + SNMPOIDGroup.OID_NAME + ":(*" + queryContext.getString(SNMPOIDGroup.OID_NAME).toLowerCase().replaceAll(" ", "").trim() + "*)) OR (+" + SNMPOIDGroup.PARENT_NAME + ":(*" + queryContext.getString(SNMPOIDGroup.OID_NAME).toLowerCase().replaceAll(" ", "").trim() + "*))) AND +" + SNMPOIDGroup.OID_TYPE + ":" + queryContext.getString(SNMPOIDGroup.OID_TYPE).toLowerCase().trim();
                    }

                    var searcher = new IndexSearcher(DirectoryReader.open(directory));

                    var queryParser = new QueryParser(query, new KeywordAnalyzer());

                    queryParser.setAllowLeadingWildcard(true);

                    var documents = searcher.search(queryParser.parse(query.trim()), SNMPOIDGroup.MAX_SEARCH);

                    LOGGER.info("query executed : " + query + " total docs : " + documents.totalHits);

                    for (var scoreDoc : documents.scoreDocs)
                    {
                        var document = searcher.doc(scoreDoc.doc);

                        if (document != null)
                        {
                            if (result.getJsonArray(document.get(SNMPOIDGroup.PARENT_OID)) == null)
                            {
                                result.put(document.get(SNMPOIDGroup.PARENT_OID), new JsonArray());
                            }

                            var item = new JsonObject().put(SNMPOIDGroup.OID_NAME, document.get(SNMPOIDGroup.OID_NAME))
                                    .put(SNMPOIDGroup.OID, document.get(SNMPOIDGroup.OID));

                            //25270
                            if (document.get(SNMPOIDGroup.PARENT_NAME) != null)
                            {
                                item.put(SNMPOIDGroup.PARENT_NAME, document.get(SNMPOIDGroup.PARENT_NAME));
                            }

                            result.getJsonArray(document.get(SNMPOIDGroup.PARENT_OID)).add(item);
                        }
                    }

                    LOGGER.info(String.format("query took %s ms time", System.currentTimeMillis() - startTime));

                    future.complete(result);

                }
                else
                {
                    future.fail(String.format(ErrorMessageConstants.API_FIELD_REQUIRED, FILTER));
                }
            }
            catch (Exception exception)
            {
                future.fail(exception);
            }

            future.future().onComplete(result ->
            {
                if (result.succeeded())
                {
                    publish(event.getString(SESSION_ID), UI_ACTION_SNMP_OID_SEARCH, event.put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK).put(RESULT, result.result()));
                }
                else
                {
                    publish(event.getString(SESSION_ID), UI_ACTION_SNMP_OID_SEARCH, event.put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, result.cause().getMessage()));
                }
            });
        }));

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_REPORT_EXPORT, message ->
        {
            var event = message.body();

            try
            {
                if (event.containsKey(ID))
                {
                    var id = CommonUtil.getLong(event.getValue(ID));

                    if (ReportCacheStore.getStore().getItem(CommonUtil.getLong(id)) == null)
                    {
                        Bootstrap.vertx().eventBus().send(EVENT_REPORT_EXPORT, event.put(ID, id));

                        publish(event.getString(SESSION_ID), UI_ACTION_REPORT_EXPORT, event
                                .put(RESPONSE_CODE, HttpStatus.SC_OK)
                                .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                .put(RESULT, new JsonObject()));
                    }
                    else
                    {
                        publish(event.getString(SESSION_ID), UI_ACTION_REPORT_EXPORT, event
                                .put(RESPONSE_CODE, SC_BAD_REQUEST)
                                .put(STATUS, GlobalConstants.STATUS_FAIL)
                                .put(MESSAGE, REPORT_EXPORT_FAILED_ALREADY_RUNNING));
                    }
                }
                else
                {
                    publish(event.getString(SESSION_ID), UI_ACTION_REPORT_EXPORT, event
                            .put(RESPONSE_CODE, SC_BAD_REQUEST)
                            .put(STATUS, GlobalConstants.STATUS_FAIL)
                            .put(MESSAGE, String.format(API_REQUEST_BAD_PARAMETER, ID)));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                publish(event.getString(SESSION_ID), UI_ACTION_REPORT_EXPORT, event.put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(GlobalConstants.MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
            }

        });

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_SESSION_ACTIVE, message -> vertx.eventBus().send(EVENT_VISUALIZATION_SESSION_ACTIVE, message.body())).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_SESSION_INACTIVE, message -> vertx.eventBus().send(EVENT_VISUALIZATION_SESSION_INACTIVE, message.body())).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_LOGON_SESSION_POPULATE, message ->
        {
            var event = message.body();

            var sessions = new JsonArray();

            ActiveUserCacheStore.getStore().getItems().forEach((key, value) ->
            {

                var item = AuthTokenConfigStore.getStore().getItemByValue(SESSION_ID, key);

                if (item != null)
                {
                    sessions.add(value.put(DURATION, CommonUtil.getLong((DateTimeUtil.currentMilliSeconds() - item.getLong(EVENT_TIMESTAMP)) / 1000))
                            .put(User.USER_NAME, UserConfigStore.getStore().getItem(value.getLong(User.USER_ID)).getString(User.USER_NAME))
                            .put(REMOTE_ADDRESS, item.getString(REMOTE_ADDRESS)));
                }
            });

            publish(event.getString(SESSION_ID), EventBusConstants.UI_ACTION_LOGON_SESSION_POPULATE, new JsonObject().put(RESULT, sessions));
        });

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_USER_SESSION_UPDATE, message ->
        {
            try
            {
                var event = message.body();

                ActiveUserCacheStore.getStore().updateTimestamp(event.getString(SESSION_ID), DateTimeUtil.currentSeconds());
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_LOGGER_LEVEL_CHANGE, message ->
        {
            var event = message.body();

            if (Bootstrap.getRegistrationId().equalsIgnoreCase(event.getString(REMOTE_EVENT_PROCESSOR_UUID)) && event.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(EVENT_APP))
            {
                LOGGER.info(String.format("Changing log-level: %s to %s", LOG_LEVELS.get(CommonUtil.getLogLevel()), LOG_LEVELS.get(event.getInteger(SYSTEM_LOG_LEVEL))));

                if (LOG_LEVEL_INFO != event.getInteger(SYSTEM_LOG_LEVEL))
                {
                    LoggerCacheStore.getStore().update(event.getJsonArray(SYSTEM_LOG_MODULES));
                }

                LogUtil.setLogLevel(event.getInteger(SYSTEM_LOG_LEVEL));

                vertx.cancelTimer(timer);

                timer = vertx.setTimer(TimeUnit.SECONDS.toMillis(CommonUtil.getLong(event.getString(LOG_LEVEL_RESET_TIMER_SECONDS))), timer ->
                {
                    LOGGER.info(String.format("Changing log-level: %s to %s", LOG_LEVELS.get(CommonUtil.getLogLevel()), LOG_LEVELS.get(LOG_LEVEL_INFO)));

                    LogUtil.setLogLevel(LOG_LEVEL_INFO);
                });
            }
            else if (event.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(MOTADATA_DATASTORE))
            {
                Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                        .put(EventBusConstants.EVENT_TOPIC, DATASTORE_OPERATION_TOPIC)
                        .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_OPERATION_TOPIC)
                        .put(REMOTE_EVENT_PROCESSOR_UUID, event.getString(REMOTE_EVENT_PROCESSOR_UUID))
                        .put(EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.MODIFY_LOG_LEVEL.getName())
                                .appendBytes(new JsonObject().put(SYSTEM_LOG_LEVEL, event.getInteger(SYSTEM_LOG_LEVEL))
                                        .put(LOG_LEVEL_RESET_TIMER_SECONDS, CommonUtil.getLong(event.getString(LOG_LEVEL_RESET_TIMER_SECONDS))).encode().getBytes()).getBytes()));
            }
            else
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.LOGGER_LEVEL_CHANGE.name())
                                .put(REMOTE_EVENT_PROCESSOR_UUID, event.getString(REMOTE_EVENT_PROCESSOR_UUID))
                                .put(SYSTEM_LOG_LEVEL, event.getInteger(SYSTEM_LOG_LEVEL))
                                .put(LOG_LEVEL_RESET_TIMER_SECONDS, CommonUtil.getInteger(event.getString(LOG_LEVEL_RESET_TIMER_SECONDS)))
                                .put(SYSTEM_LOG_MODULES, event.getJsonArray(SYSTEM_LOG_MODULES))
                );
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_LOGGER_LEVEL_GET, message ->
        {
            var event = message.body();

            publish(event.getString(SESSION_ID), UI_ACTION_LOGGER_LEVEL_GET, event.put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(SYSTEM_LOG_LEVEL, CommonUtil.getLogLevel()).put(SYSTEM_LOG_MODULES, LoggerCacheStore.getStore().getItems()));
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_USER_NAVIGATE, message ->
        {
            var event = message.body();

            ActiveUserCacheStore.getStore().updateItem(event.getString(SESSION_ID), event.getJsonObject(EVENT_CONTEXT).put(LAST_ACTIVE_TIMESTAMP, DateTimeUtil.currentSeconds()));
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_SHARE, message ->
        {
            try
            {
                var event = message.body();

                if (event.containsKey(PolicyEngineConstants.RECIPIENTS) && !event.getJsonArray(PolicyEngineConstants.RECIPIENTS).isEmpty())
                {
                    var recipients = event.getJsonArray(PolicyEngineConstants.RECIPIENTS);

                    var type = event.getString("type");
                    // Will check if share type is policy with this parameter as alert will be shared with same format of mail and message while it get trigger
                    // For that we are fetching all data of policy to replace in templates
                    var context = enrich(event);

                    var fileName = event.getString(APIConstants.FILENAME);

                    if (Notification.ShareType.ALERT.getName().equalsIgnoreCase(type) || Bootstrap.vertx().fileSystem().existsBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName))
                    {
                        var emailRecipients = new HashSet<String>();

                        var channelRecipients = new HashMap<String, Set<String>>();

                        setRecipients(recipients, emailRecipients, channelRecipients);

                        context.put(POLICY_URL, String.format(POLICY_URL_VALUE, Base64.getEncoder().encodeToString(new JsonObject().put(POLICY_DRILL_DOWN_TEMPLATE, YES).put(PolicyEngineConstants.POLICY_ID, context.getString(PolicyEngineConstants.POLICY_ID, EMPTY_VALUE)).put(EVENT_SOURCE, PolicyEngineConstants.PolicyType.TRAP.getName().equalsIgnoreCase(event.getString(POLICY_TYPE)) ? event.getString(EVENT_SOURCE) : MotadataConfigUtil.getHost()).put(ENTITY_ID, event.getLong(Entity.OBJECT.getName().toLowerCase())).put(INSTANCE, context.getString(INSTANCE, EMPTY_VALUE)).put(METRIC, context.getString(METRIC, EMPTY_VALUE)).put(EVENT_SOURCE, event.getString("eventSource", EMPTY_VALUE)).put(POLICY_TYPE, type).encode().getBytes())));

                        if (!emailRecipients.isEmpty())
                        {
                            var attachments = new JsonArray().addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS);
                            // Alert share type will not have attachment
                            if (!Notification.ShareType.ALERT.getName().equalsIgnoreCase(type))
                            {
                                attachments.add(fileName);
                            }
                            else
                            {
                                attachments.add(context.getString(SEVERITY).toLowerCase() + ".png");
                            }

                            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline")
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                    .put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                                    .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray(new ArrayList<>(emailRecipients)))
                                    .put(Notification.EMAIL_NOTIFICATION_MESSAGE, event.getString(MESSAGE, EMPTY_VALUE))
                                    .put(Notification.EMAIL_NOTIFICATION_SUBJECT, new StringSubstitutor(context.getMap()).replace(Notification.getNotificationSubject(Notification.NotificationType.EMAIL.getName(), type)))
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, attachments)
                                    .put(Notification.TEMPLATE_NAME, Notification.getNotificationTemplate(Notification.NotificationType.EMAIL.getName(), type, event.getString(PolicyEngineConstants.POLICY_TYPE)))
                                    .put(Notification.EMAIL_NOTIFICATION_CONTENT, context));

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("For entity %s, email sent to %s, from username %s", event.getString("type"), emailRecipients, event.getString(User.USER_NAME)));
                            }
                        }

                        if (!channelRecipients.isEmpty())
                        {
                            channelRecipients.forEach((channelType, targets) ->
                            {
                                if (!targets.isEmpty())
                                {
                                    if (!Notification.ShareType.ALERT.getName().equalsIgnoreCase(type))
                                    {
                                        context.put("content", Base64.getEncoder().encodeToString(vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + CommonUtil.getString(fileName)).getBytes()));
                                    }

                                    Notification.send(new JsonObject()
                                            .put(NOTIFICATION_TYPE, channelType)
                                            .put(SEVERITY, context.getString(SEVERITY, EMPTY_VALUE).toLowerCase())
                                            .put(PolicyEngineConstants.CHANNELS, new JsonArray(new ArrayList<>(targets)))
                                            .put(Notification.CHANNEL_NOTIFICATION_CONTENT, new StringSubstitutor(context.getMap()).replace(Notification.getNotificationTemplate(channelType, type, event.getString(PolicyEngineConstants.POLICY_TYPE)))));
                                }
                            });

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("For entity %s, Channel Message sent to %s, from username %s", event.getString("type"), channelRecipients, event.getString(User.USER_NAME)));
                            }
                        }
                    }
                    else
                    {
                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("File: %s not found", fileName));
                        }
                    }

                }
                else
                {
                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("No recipients found to share the %s", event.getString("type")));
                    }

                }

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        /*
         * This event will give policy data based on policy attributes.
         * This will help to standardize the policy drill down view and help in sharing URL in Notifications.
         */
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.UI_ACTION_POLICY_QUERY, message ->
        {
            var event = message.body();

            var future = Promise.<JsonObject>promise();

            try
            {

                var context = new JsonObject();

                if ((PolicyEngineConstants.PolicyType.TRAP.getName().equalsIgnoreCase(event.getString(PolicyEngineConstants.POLICY_TYPE))
                        || PolicyEngineConstants.PolicyType.LOG.getName().equalsIgnoreCase(event.getString(PolicyEngineConstants.POLICY_TYPE))
                        || PolicyEngineConstants.PolicyType.FLOW.getName().equalsIgnoreCase(event.getString(PolicyEngineConstants.POLICY_TYPE)))
                        && EventPolicyConfigStore.getStore().existItem(event.getLong(PolicyEngineConstants.POLICY_ID)))
                {
                    context.mergeIn(EventPolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID)));

                    future.complete(new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESULT, EventPolicyCacheStore.getStore().getTriggerTicks(event.getLong(PolicyEngineConstants.POLICY_ID)).mergeIn(context).put(POLICY_ID, event.getLong(PolicyEngineConstants.POLICY_ID))));
                }
                else if (MetricPolicyConfigStore.getStore().existItem(event.getLong(PolicyEngineConstants.POLICY_ID)))
                {
                    context.mergeIn(MetricPolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID)));

                    if (ObjectConfigStore.getStore().existItem(event.getLong(ENTITY_ID)))
                    {
                        context.mergeIn(ObjectConfigStore.getStore().getItem(event.getLong(ENTITY_ID)));
                    }

                    var key = event.getLong(ENTITY_ID) + SEPARATOR + event.getLong(PolicyEngineConstants.POLICY_ID) + SEPARATOR + (event.getValue(INSTANCE, null) != null && !CommonUtil.getString(event.getValue(INSTANCE)).isEmpty() ? event.getString(GlobalConstants.METRIC) + SEPARATOR + event.getValue(INSTANCE) : event.getString(GlobalConstants.METRIC));

                    Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY, new JsonObject().put(PolicyEngineConstants.POLICY_KEY, key), reply ->
                    {
                        try
                        {
                            var duration = reply.result().body();

                            if (duration != null && !duration.isEmpty())
                            {
                                future.complete(new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESULT, context.mergeIn(duration)));
                            }
                            else
                            {
                                future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR, "No record(s) found.")
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            future.fail(exception);
                        }
                    });
                }

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.complete(event.put(STATUS, STATUS_FAIL)
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
            }

            future.future().onComplete(asyncResult ->
            {
                if (asyncResult.succeeded())
                {
                    var result = asyncResult.result();

                    if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED) && result.getJsonObject(RESULT) != null)
                    {
                        EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_ACTION_POLICY_QUERY, event.put(RESULT, result.getJsonObject(RESULT)));
                    }
                    else
                    {
                        EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_ACTION_POLICY_QUERY, event);
                    }

                }
                else
                {
                    EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_ACTION_POLICY_ACTIVE_FLAP_GET, event.put(RESULT, new JsonObject()));
                }

            });

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_STORAGE_PROFILE_TEST, message ->
        {
            var eventId = CommonUtil.newEventId();

            var event = message.body();

            event.put(EVENT_ID, eventId);

            vertx.<JsonObject>executeBlocking(future ->
            {
                EventBusConstants.startEvent(eventId, Thread.currentThread().getName());

                if (event.containsKey(StorageProfile.STORAGE_PROFILE_CONTEXT))
                {
                    event.mergeIn(event.getJsonObject(StorageProfile.STORAGE_PROFILE_CONTEXT)).remove(StorageProfile.STORAGE_PROFILE_CONTEXT);
                }

                event.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(event.getString(AIOpsObject.OBJECT_TARGET)))
                        .put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray()
                                .add(RemoteEventProcessorConfigStore.getStore().flatItemsByValue(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name(), REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode()).getJsonObject(0).getLong(ID)))
                        .put(EventBusConstants.EVENT_REPLY, YES).put(TIMEOUT, event.getInteger(TIMEOUT, 60))
                        .put(DEST_FILE_NAME, SAMPLE_FILE)
                        .put(SRC_FILE_PATH, CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + SAMPLE_FILE);

                var protocol = StorageProfile.StorageProtocol.valueOfName(event.getString(StorageProfile.STORAGE_PROFILE_PROTOCOL));

                if (StorageProfile.StorageProtocol.FTP == protocol || StorageProfile.StorageProtocol.TFTP == protocol || StorageProfile.StorageProtocol.SFTP == protocol)
                {
                    var id = Runbook.getStorageProfileRunbookId(protocol);

                    if (id != GlobalConstants.DUMMY_ID)
                    {
                        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_RUNBOOK, event.put(ID, id), DELIVERY_OPTIONS, reply ->
                        {
                            try
                            {
                                if (reply.succeeded())
                                {
                                    event.mergeIn(reply.result().body());

                                    var replyContexts = event.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS);

                                    if (replyContexts != null && !replyContexts.isEmpty())
                                    {
                                        event.mergeIn(replyContexts.getJsonObject(0));

                                        extractError(event);

                                        if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                        {
                                            event.put(MESSAGE, InfoMessageConstants.STORAGE_PROFILE_TEST_SUCCEEDED);
                                        }
                                        else
                                        {
                                            extractErrorCode(event, STORAGE_PROFILE_TEST_FAILED, ErrorCodes.ERROR_CODE_STORAGE_TEST, null, null);
                                        }
                                    }
                                    else
                                    {
                                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(STORAGE_PROFILE_TEST_FAILED, "reply contexts not found"));
                                    }
                                }
                                else
                                {
                                    event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(STORAGE_PROFILE_TEST_FAILED, reply.cause().getMessage()))
                                            .put(ERROR, CommonUtil.formatStackTrace(reply.cause().getStackTrace()));
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                future.complete(event.put(STATUS, STATUS_FAIL)
                                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
                            }

                            future.complete(event);
                        });
                    }
                }
                else if (StorageProfile.StorageProtocol.LOCAL == protocol)
                {
                    CommonUtil.copy(event);

                    if (event.containsKey(MESSAGE))
                    {
                        future.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, event.getString(MESSAGE)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_RESPONSE));
                    }
                    else
                    {
                        future.complete(event.put(GlobalConstants.STATUS, STATUS_SUCCEED).put(MESSAGE, InfoMessageConstants.STORAGE_PROFILE_TEST_SUCCEEDED));
                    }
                }
                else
                {
                    future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, "Invalid Storage Protocol"));
                }

            }, result -> notify(result.result(), UI_ACTION_STORAGE_PROFILE_TEST));

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_CONFIG_TEMPLATE_EXPORT, message ->
        {
            var event = message.body();

            try
            {
                if (event.containsKey(ID))
                {
                    vertx.<JsonObject>executeBlocking(future ->
                            {
                                var id = CommonUtil.getLong(event.getValue(ID));

                                var template = ConfigTemplateConfigStore.getStore().getItem(id);

                                event.put(FILE_NAME, template.getString(ConfigTemplate.CONFIG_TEMPLATE_NAME) + ".json");

                                vertx.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + event.getString(FILE_NAME), Buffer.buffer(CommonUtil.removeSensitiveFields(template, true).encodePrettily()));

                                future.complete(event);

                            }, false,
                            result -> publish(result.result().getString(SESSION_ID), EventBusConstants.UI_NOTIFICATION_CSV_EXPORT_READY, result.result().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)));
                }
                else
                {
                    publish(event.getString(SESSION_ID), UI_ACTION_CONFIG_TEMPLATE_EXPORT, event
                            .put(RESPONSE_CODE, SC_BAD_REQUEST)
                            .put(STATUS, GlobalConstants.STATUS_FAIL)
                            .put(ERROR, String.format(API_REQUEST_BAD_PARAMETER, ID))
                            .put(MESSAGE, String.format(API_REQUEST_BAD_PARAMETER, ID)));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                publish(event.getString(SESSION_ID), UI_ACTION_CONFIG_TEMPLATE_EXPORT, event.put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(GlobalConstants.MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
            }

        });

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_ARTIFACT_COMPATIBILITY_CHECK, message ->
        {
            var future = Promise.<JsonArray>promise();

            var event = message.body();

            try
            {
                var version = getVersion(event.getString(PATCH_ARTIFACT_FILE));

                if (version != null && !version.isEmpty())
                {
                    var types = event.getJsonArray(ARTIFACT_TYPE);

                    var upgradableItems = new JsonArray();

                    for (var typeIndex = 0; typeIndex < types.size(); typeIndex++)
                    {
                        if (types.getString(typeIndex).equalsIgnoreCase(BootstrapType.AGENT.name()))
                        {
                            var items = AgentConfigStore.getStore().getItems();

                            for (var index = 0; index < items.size(); index++)
                            {
                                var item = items.getJsonObject(index);

                                var artifact = ArtifactConfigStore.getStore().getItem(item.getLong(ID));

                                if (artifact != null)
                                {
                                    if (MotadataConfigUtil.compatible(BootstrapType.AGENT.name().toLowerCase(), version))
                                    {
                                        if (MotadataConfigUtil.upgradable(version, item.getString(AGENT_VERSION)))
                                        {
                                            artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_UPGRADE_REQUIRED);
                                        }
                                        else
                                        {
                                            artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS);
                                        }
                                    }
                                    else
                                    {
                                        artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_NOT_COMPATIBLE);
                                    }

                                    item.mergeIn(artifact);
                                }
                            }

                            upgradableItems.addAll(items);
                        }
                        else
                        {
                            var items = RemoteEventProcessorConfigStore.getStore().getItemsByValue(REMOTE_EVENT_PROCESSOR_TYPE, types.getString(typeIndex));

                            var isMaster = types.getString(typeIndex).equalsIgnoreCase(BootstrapType.APP.name());

                            for (var index = 0; index < items.size(); index++)
                            {
                                var item = items.getJsonObject(index);

                                var artifact = ArtifactConfigStore.getStore().getItem(item.getLong(ID));

                                if (artifact != null)
                                {
                                    if (isMaster || MotadataConfigUtil.compatible(item.getString(REMOTE_EVENT_PROCESSOR_TYPE), version))
                                    {
                                        if (MotadataConfigUtil.upgradable(version, item.getString(REMOTE_EVENT_PROCESSOR_VERSION)))
                                        {
                                            artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_UPGRADE_REQUIRED);
                                        }
                                        else
                                        {
                                            artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS);
                                        }
                                    }
                                    else
                                    {
                                        artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_NOT_COMPATIBLE);
                                    }

                                    item.mergeIn(artifact);
                                }
                            }

                            upgradableItems.addAll(items);
                        }
                    }

                    future.complete(upgradableItems);
                }
                else
                {
                    future.fail("compatibility check failed, reason: version not found");
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.fail(exception);
            }

            future.future().onComplete(result ->
            {
                if (result.succeeded())
                {
                    publish(event.getString(SESSION_ID), UI_ACTION_ARTIFACT_COMPATIBILITY_CHECK, event.put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK).put(RESULT, result.result()));
                }
                else
                {
                    publish(event.getString(SESSION_ID), UI_ACTION_ARTIFACT_COMPATIBILITY_CHECK, event
                            .put(RESPONSE_CODE, SC_BAD_REQUEST).put(STATUS, STATUS_FAIL)
                            .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage())));
                }
            });
        });

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_INTEGRATION_TEST, message ->
        {
            try
            {
                var event = message.body();

                vertx.eventBus().<JsonObject>request(event.getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE) + DOT_SEPARATOR + EVENT_INTEGRATION_TEST, event, DELIVERY_OPTIONS, reply ->
                {
                    if (reply.succeeded())
                    {
                        publish(event.getString(SESSION_ID), UI_ACTION_INTEGRATION_TEST, event.mergeIn(reply.result().body()));
                    }
                    else
                    {
                        publish(event.getString(SESSION_ID), UI_ACTION_INTEGRATION_TEST, event.mergeIn(new JsonObject().put(STATUS, STATUS_FAIL).put(MESSAGE, reply.cause().getMessage())));
                    }
                });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        // This is a generic event handler for manual synchronization, which forwards the event based on the integration type.
        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_INTEGRATION_SYNC, message ->
        {
            try
            {
                var event = message.body();

                var eventId = CommonUtil.newEventId();

                event.put(EventBusConstants.EVENT_ID, eventId);

                vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_ACTION_INTEGRATION_SYNC)
                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                        .put(USER_NAME, event.getString(USER_NAME))
                        .put(EventBusConstants.EVENT_ID, eventId));

                vertx.eventBus().<JsonObject>request(event.getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE) + DOT_SEPARATOR + EVENT_INTEGRATION_SYNC, event, DELIVERY_OPTIONS, reply ->
                {
                    if (reply.succeeded())
                    {
                        notify(event.mergeIn(reply.result().body()).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS), UI_ACTION_INTEGRATION_SYNC);
                    }
                    else
                    {
                        notify(event.mergeIn(new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(MESSAGE, reply.cause().getMessage())), UI_ACTION_INTEGRATION_SYNC);
                    }
                });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(UI_INTEGRATION_PROFILE_TEST, message ->
        {
            LOGGER.info(String.format("integration profile test : %s ", message.body().encode()));

            var event = message.body();

            var eventId = CommonUtil.newEventId();

            event.put(EventBusConstants.EVENT_ID, eventId);

            try
            {
                vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_INTEGRATION_PROFILE_TEST)
                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                        .put(USER_NAME, event.getString(USER_NAME))
                        .put(EventBusConstants.EVENT_ID, eventId));

                event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_INTEGRATION_PROFILE_TEST);

                var entity = new JsonObject();

                var valid = true;


                if (event.getString(REQUEST_PARAM_TYPE).equalsIgnoreCase(REQUEST_CREATE))
                {
                    entity.mergeIn(event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT)).put(IntegrationProfile.INTEGRATION_PROFILE_NAME, event.getString(IntegrationProfile.INTEGRATION_PROFILE_NAME))
                            .put(IntegrationProfile.INTEGRATION, event.getString(IntegrationProfile.INTEGRATION));
                }

                else
                {
                    if (IntegrationProfileConfigStore.getStore().existItem(event.getLong(ID)))
                    {
                        var item = IntegrationProfileConfigStore.getStore().getItem(event.getLong(ID));

                        if (item != null && !item.isEmpty())
                        {
                            item.mergeIn(item.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT));

                            entity.mergeIn(item);
                        }
                    }

                    else
                    {
                        valid = false;

                        event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.INTEGRATION_PROFILE_TEST_FAILED_NOT_FOUND);

                        notify(event, EventBusConstants.UI_INTEGRATION_PROFILE_TEST);
                    }
                }

                if (valid)
                {
                    if (event.containsKey(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT))
                    {
                        entity.mergeIn(event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT));

                        entity.remove(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT);
                    }

                    event.remove(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT);

                    var integrationId = IntegrationConstants.IntegrationId.valueOfName(event.getLong(IntegrationProfile.INTEGRATION));

                    if (IntegrationConstants.IntegrationId.MICROSOFT_TEAMS == integrationId)
                    {
                        var integration = IntegrationConfigStore.getStore().getItem(integrationId.getName());

                        entity.mergeIn(integration);

                        if (entity.containsKey(Integration.INTEGRATION_CONTEXT))
                        {
                            entity.mergeIn(entity.getJsonObject(Integration.INTEGRATION_CONTEXT));

                            entity.remove(Integration.INTEGRATION_CONTEXT);
                        }

                        if (entity.containsKey(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE) && !entity.getString(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE).trim().isEmpty() && CredentialProfileConfigStore.getStore().existItem(entity.getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE)))
                        {
                            var item = CredentialProfileConfigStore.getStore().getItem(entity.getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE));

                            entity.mergeIn(item);

                            var url = entity.getString(GlobalConstants.TARGET, EMPTY_VALUE) + String.format(TeamsIntegration.MESSAGE_END_POINT, entity.getString(TeamsIntegration.TEAM, EMPTY_VALUE), entity.getString(TeamsIntegration.CHANNEL, EMPTY_VALUE));

                            var future = Promise.<JsonObject>promise();

                            Bootstrap.vertx().eventBus().<String>request(EventBusConstants.EVENT_OAUTH_TOKEN_GENERATE, item,
                                    new DeliveryOptions().setSendTimeout(15 * 1000L),
                                    asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            WebClientUtil.getWebClient().postAbs(url)
                                                    .timeout(TimeUnit.SECONDS.toMillis(entity.getLong(GlobalConstants.TIMEOUT, 60L)))
                                                    .bearerTokenAuthentication(asyncResult.result().body())
                                                    .sendJson(new JsonObject("{\"subject\": null,\"body\": {\"contentType\": \"html\",\"content\": \"<p>This a test message from Motadata.<p>\"}}"), response ->
                                                    {
                                                        if (response.succeeded())
                                                        {
                                                            var result = response.result();

                                                            if (result.statusCode() == HttpStatus.SC_OK || result.statusCode() == HttpStatus.SC_CREATED)
                                                            {
                                                                future.complete(event.put(STATUS, STATUS_SUCCEED)
                                                                        .put(MESSAGE, InfoMessageConstants.INTEGRATION_PROFILE_TEST_SUCCEEDED));
                                                            }
                                                            else
                                                            {
                                                                future.complete(event.put(STATUS, STATUS_FAIL)
                                                                        .put(MESSAGE, String.format(ErrorMessageConstants.INTEGRATION_PROFILE_TEST_FAILED, String.format(INVALID_STATUS_CODE, result.statusCode()))));
                                                            }
                                                        }
                                                        else
                                                        {
                                                            LOGGER.warn(String.format(ErrorMessageConstants.INTEGRATION_PROFILE_TEST_FAILED, response.cause()));

                                                            future.complete(event.put(STATUS, STATUS_FAIL)
                                                                    .put(MESSAGE, String.format(ErrorMessageConstants.INTEGRATION_PROFILE_TEST_FAILED, response.cause())));

                                                        }

                                                    });
                                        }
                                        else
                                        {
                                            LOGGER.error(asyncResult.cause());

                                            future.complete(event.put(STATUS, STATUS_FAIL)
                                                    .put(MESSAGE, String.format(ErrorMessageConstants.INTEGRATION_PROFILE_TEST_FAILED, asyncResult.cause())));
                                        }
                                    });

                            future.future().onComplete(
                                    result -> notify(result.result(), EventBusConstants.UI_INTEGRATION_PROFILE_TEST));
                        }
                        else
                        {
                            event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.INTEGRATION_PROFILE_TEST_FAILED_INTEGRATION_NOT_CONFIGURED);

                            notify(event, EventBusConstants.UI_INTEGRATION_PROFILE_TEST);
                        }

                    }
                }

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                event.put(STATUS, STATUS_FAIL)
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage()));

                notify(event, EventBusConstants.UI_INTEGRATION_PROFILE_TEST);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_INTEGRATION_RESPONSE_GET, message ->
        {
            try
            {
                var event = message.body();

                var integrationType = IntegrationCacheStore.getStore().getIntegration(event.getValue(IntegrationEngine.ACK_ID));

                if (CommonUtil.isNotNullOrEmpty(integrationType))
                {
                    vertx.eventBus().<JsonObject>request(integrationType + DOT_SEPARATOR + EVENT_INTEGRATION_RESPONSE_GET, event.put(IntegrationEngine.ACK_ID, event.getString(IntegrationEngine.ACK_ID).split(HASH_SEPARATOR)[1]), DELIVERY_OPTIONS, reply ->
                    {
                        if (reply.succeeded())
                        {
                            if (reply.result().body().containsKey(STATUS) && STATUS_FAIL.equalsIgnoreCase(reply.result().body().getString(STATUS)))
                            {
                                publish(event.getString(SESSION_ID), UI_ACTION_INTEGRATION_RESPONSE_GET, event.mergeIn(reply.result().body().put(STATUS, STATUS_FAIL).put(MESSAGE, INTERNAL_ERROR)));
                            }
                            else
                            {
                                publish(event.getString(SESSION_ID), UI_ACTION_INTEGRATION_RESPONSE_GET, event.mergeIn(reply.result().body().put(STATUS, STATUS_SUCCEED)));
                            }
                        }
                        else
                        {
                            publish(event.getString(SESSION_ID), UI_ACTION_INTEGRATION_RESPONSE_GET, event.mergeIn(new JsonObject().put(STATUS, STATUS_FAIL).put(MESSAGE, reply.cause().getMessage())));
                        }
                    });
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_DECLARE_INCIDENT, message -> vertx.eventBus().send(EVENT_INTEGRATION, message.body()));

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_OBJECT_POLLING_ERROR_QUERY, message ->
                NMSConstants.getFaultyObjects(false).onComplete(result ->
                {

                    if (result.succeeded())
                    {
                        notify(message.body().mergeIn(new JsonObject().put(RESULT, result.result()).put(STATUS, STATUS_SUCCEED)), UI_ACTION_OBJECT_POLLING_ERROR_QUERY);
                    }

                })).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_ARCHIVE_ENABLE_GET, message ->
        {
            var event = message.body();

            publish(event.getString(SESSION_ID), UI_ACTION_ARCHIVE_ENABLE_GET, event.put("archive.enabled", MotadataConfigUtil.archivedObjectEnabled()));

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_DIAGNOSTIC_RUN, message ->
        {
            var event = message.body();

            LOGGER.info(String.format("Diagnostics data collection for processor type: %s processor uuid: %s ", event.getString(REMOTE_EVENT_PROCESSOR_TYPE), event.getString(REMOTE_EVENT_PROCESSOR_UUID)));

            if (Bootstrap.getRegistrationId().equalsIgnoreCase(event.getString(REMOTE_EVENT_PROCESSOR_UUID)) && event.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(EVENT_APP))
            {
                vertx.eventBus().send(EVENT_DIAGNOSTIC, event);
            }
            else if (event.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(MOTADATA_DATASTORE))
            {
                Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                        .put(EventBusConstants.EVENT_TOPIC, DATASTORE_CONNECTION_ALIVE_TOPIC)
                        .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_OPERATION_TOPIC)
                        .put(REMOTE_EVENT_PROCESSOR_UUID, event.getString(REMOTE_EVENT_PROCESSOR_UUID))
                        .put(EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.PROFILER.getName()).getBytes()));

                Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                        .put(EventBusConstants.EVENT_TOPIC, DATASTORE_BROKER_OPERATION_TOPIC)
                        .put(REMOTE_EVENT_PROCESSOR_UUID, event.getString(REMOTE_EVENT_PROCESSOR_UUID))
                        .put(EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.PROFILER.getName()).getBytes()));
            }
            else
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DIAGNOSTIC)
                                .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                .put(REMOTE_EVENT_PROCESSOR_UUID, event.getString(REMOTE_EVENT_PROCESSOR_UUID))
                                .put(REMOTE_EVENT_PROCESSOR_TYPE, event.getString(REMOTE_EVENT_PROCESSOR_TYPE)));
            }

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_SSH_CLIENT_MANAGER, message ->
                vertx.eventBus().send(EVENT_SSH_CLIENT_MANAGER, message.body()));

        vertx.eventBus().<JsonObject>localConsumer(UI_NOTIFICATION_ACTIVE_NOTIFICATION_CLEAR, message ->
        {
            LOGGER.info(String.format("clear alert notification : %s ", message.body().encode()));

            vertx.eventBus().<JsonObject>request(EVENT_ACTIVE_NOTIFICATION_CLEAR, message.body(), reply ->
            {
                if (reply.succeeded())
                {
                    EventBusConstants.publish(message.body().getString(SESSION_ID), UI_NOTIFICATION_ACTIVE_NOTIFICATION_QUERY, new JsonObject().put(ENTITY_PROPERTY_COUNT, 0));
                }
            });

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_TAG_RULE_TEST, handler ->
        {

            var event = handler.body();

            vertx.eventBus().<JsonObject>request(EVENT_TAG_RULE_RUN, event.put(TagRule.TAG_RULE_TEST, true), DELIVERY_OPTIONS, reply ->
            {
                try
                {
                    if (reply.succeeded())
                    {
                        event.mergeIn(reply.result().body());

                        if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                        {
                            event.put(MESSAGE, InfoMessageConstants.TAG_RULE_CONDITION_SATISFIED);
                        }
                        else
                        {
                            event.put(MESSAGE, InfoMessageConstants.TAG_RULE_CONDITION_NOT_SATISFIED);
                        }
                    }
                    else
                    {
                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, reply.cause().getMessage())
                                .put(ERROR, CommonUtil.formatStackTrace(reply.cause().getStackTrace()));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                notify(event, EventBusConstants.UI_ACTION_TAG_RULE_TEST);
            });
        });

        vertx.eventBus().<JsonObject>localConsumer(UI_ACTION_TAG_RULE_RUN, handler ->
        {

            var event = handler.body();

            //In run UI will only send ID of Tag rule
            event.mergeIn(TagRuleConfigStore.getStore().getItem(event.getLong(ID)));

            vertx.eventBus().<JsonObject>request(EVENT_TAG_RULE_RUN, event.put(TagRule.TAG_RULE_TEST, false), DELIVERY_OPTIONS, reply ->
            {
                try
                {
                    if (reply.succeeded())
                    {
                        event.mergeIn(reply.result().body());

                        if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                        {
                            event.put(MESSAGE, InfoMessageConstants.TAG_RULE_RUN_SUCCEEDED);
                        }
                        else
                        {
                            event.put(STATUS, InfoMessageConstants.TAG_RULE_RUN_FAILED);
                        }
                    }
                    else
                    {
                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, InfoMessageConstants.TAG_RULE_RUN_FAILED)
                                .put(ERROR, CommonUtil.formatStackTrace(reply.cause().getStackTrace()));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                notify(event, EventBusConstants.UI_ACTION_TAG_RULE_RUN);

            });
        });
        promise.complete();
    }

    private String getVersion(String fileName)
    {
        String version = null;

        try (var file = new ZipFile(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + fileName))
        {
            var entry = file.getEntry(VERSION_FILE);

            if (entry != null && entry.getSize() < MAX_VERSION_FILE_SIZE_BYTES)
            {
                var reader = new BufferedReader(new InputStreamReader(file.getInputStream(entry)));

                var line = reader.readLine();

                if (CommonUtil.isNotNullOrEmpty(line))
                {
                    version = line.trim().split(NEW_LINE)[0].trim();
                }

                reader.close();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return version;
    }

    private void enrich(JsonObject event, String metricType, String filter, String type)
    {
        var credential = event.containsKey(filter) ? CredentialProfileConfigStore.getStore().getItem(event.getLong(filter)) : null;

        var metric = new JsonObject();

        if (NMSConstants.Type.PING.getName().equalsIgnoreCase(event.getString(MetricPlugin.METRIC_PLUGIN_TYPE)) && NMSConstants.Category.OTHER.getName().equalsIgnoreCase(event.getString(AIOpsObject.OBJECT_CATEGORY)))
        {
            metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(event.getLong(ID), NMSConstants.MetricPlugin.AVAILABILITY.getName()));
        }
        else
        {
            metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(event.getLong(ID),
                    NMSConstants.getMetricPlugin(NMSConstants.Type.valueOfName(type != null ? type : event.getString(metricType, event.getString(AIOpsObject.OBJECT_TYPE))))));
        }
        if (metric != null)
        {
            if (metric.containsKey(Metric.METRIC_CONTEXT))
            {
                metric.mergeIn(metric.getJsonObject(Metric.METRIC_CONTEXT));
            }

            metric.remove(Metric.METRIC_CONTEXT);

            event.mergeIn(metric);

            if (credential == null)
            {
                credential = CredentialProfileConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE));
            }
        }

        if (credential != null && !credential.isEmpty())
        {
            credential.mergeIn(credential.getJsonObject(CREDENTIAL_PROFILE_CONTEXT));

            credential.remove(CREDENTIAL_PROFILE_CONTEXT);

            event.mergeIn(credential);
        }

        if (event.containsKey(AIOpsObject.OBJECT_AGENT)) // for agent based test event update metric type if not present
        {
            event.put(Metric.METRIC_TYPE, event.containsKey(metricType) ? event.getString(metricType) : event.getString(AIOpsObject.OBJECT_TYPE));
        }
    }

    private void notify(JsonObject event, String eventType)
    {
        if (!event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
        {
            event.put(ERRORS, event.containsKey(ERROR) ? new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(ERROR, event.getValue(ERROR)).put(MESSAGE, event.getString(MESSAGE)).put(ERROR_CODE, event.getString(ERROR_CODE)))
                    : new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(MESSAGE, event.getString(MESSAGE)).put(ERROR_CODE, event.getString(ERROR_CODE))));
        }

        vertx.eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID))
                .put(EventBusConstants.EVENT_CONTEXT, event));

        complete(event);

        publish(event.getString(SESSION_ID), eventType, event);
    }

    private void sendEmail(JsonObject event, MailConfig configs, MailMessage mailMessage, Promise<JsonObject> promise)
    {
        var mailClient = MailClient.create(Bootstrap.vertx(), configs);

        mailClient.sendMail(mailMessage, result ->
        {
            try
            {
                mailClient.close();

                if (result.succeeded())
                {
                    promise.complete(event.put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(MESSAGE, InfoMessageConstants.MAIL_SERVER_TEST_SUCCEEDED));
                }
                else
                {
                    promise.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_EMAIL_CONFIG_TEST).put(MESSAGE, String.format(MAIL_SERVER_TEST_FAILED, result.cause().getMessage())).put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                }

            }
            catch (Exception exception)
            {
                LOGGER.warn(String.format("Mail Send Event Failed Reason : %s", CommonUtil.formatStackTrace(exception.getStackTrace())));

                promise.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_EMAIL_CONFIG_TEST).put(MESSAGE, String.format(MAIL_SERVER_TEST_FAILED, result.cause().getMessage())).put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
            }
        });
    }

    private void buildMessage(JsonObject event, MailConfig configs, MailMessage mailMessage)
    {
        mailMessage.setFrom(event.getString(MailServerConfiguration.MAIL_SERVER_USERNAME));

        if (CommonUtil.isNotNullOrEmpty(event.getString(MailServerConfiguration.MAIL_SERVER_PROTOCOL)))
        {
            if (event.getString(MailServerConfiguration.MAIL_SERVER_PROTOCOL).equalsIgnoreCase(MailServerConfiguration.MAIL_SERVER_PROTOCOL_SSL))
            {
                configs.setSsl(true);
            }

            if (event.getString(MailServerConfiguration.MAIL_SERVER_PROTOCOL).equalsIgnoreCase(MailServerConfiguration.MAIL_SERVER_PROTOCOL_TLS))
            {
                configs.setStarttls(StartTLSOptions.REQUIRED);
            }
        }

        if (CommonUtil.isNotNullOrEmpty(event.getString(MailServerConfiguration.MAIL_SERVER_SENDER)))
        {
            mailMessage.setFrom(event.getString(MailServerConfiguration.MAIL_SERVER_SENDER));
        }

        //#3515 - Outlook.com no longer supports AUTH PLAIN authentication, Outlook.com allows users to authenticate using AUTH LOGIN, which most email clients support.

        mailMessage.setSubject("Motadata : Email Server Configuration Test");

        mailMessage.setText("This a test mail from Motadata.");

        if (mailMessage.getFrom() == null || mailMessage.getFrom().isEmpty())
        {
            mailMessage.setFrom("<EMAIL>");
        }

        if (CommonUtil.isNotNullOrEmpty(event.getString("target")))
        {
            mailMessage.setTo(event.getString("target"));
        }
        else
        {
            mailMessage.setTo("<EMAIL>");
        }

    }

    @Override
    public void stop(Promise<Void> promise)
    {
        LOGGER.info("UIActionEventHandler undeploy ... ");

        promise.complete();
    }


}
