/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.policy;

import com.mindarray.*;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.api.Tag;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.MetricPolicyConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.TagConfigStore;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.METRIC_POLICY_API_ENDPOINT;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.notification.Notification.NOTIFICATION_TYPE;
import static com.mindarray.policy.PolicyEngineConstants.*;
import static com.mindarray.visualization.VisualizationConstants.QUERY_ID;
import static com.mindarray.visualization.VisualizationConstants.QUERY_PROGRESS;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@Timeout(90 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestAIOpsPolicy
{

    private static final AtomicBoolean UNSUPPRESSED = new AtomicBoolean(false);
    private static final JsonObject IDS = new JsonObject();
    private static final Logger LOGGER = new Logger(TestAIOpsPolicy.class, MOTADATA_API, "Test AIOps Policy");
    public static MessageConsumer<JsonObject> messageConsumer;
    public static MessageConsumer<JsonObject> consumer;
    private static JsonObject object = new JsonObject();
    private static JsonObject linuxObject = new JsonObject();
    private static JsonObject networkObject = new JsonObject();

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        linuxObject = ObjectConfigStore.getStore().getItemsByValues(AIOpsObject.OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.LINUX.getName())).getJsonObject(0);

        networkObject = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        consumer = TestUtil.vertx().eventBus().localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            if (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.UNSUPPRESS_POLICY && message.body().getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.BASELINE.getName()))
            {
                consumer.unregister(result -> UNSUPPRESSED.set(true));
            }
        });

        if (object == null || linuxObject == null || networkObject == null)
        {
            testContext.failNow("either object or linuxObject or networkObject is null");
        }

        testContext.completeNow();
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        MetricConfigStore.getStore().deleteItem(1234567L);

        if (UNSUPPRESSED.get())
        {
            testContext.completeNow();
        }

        else
        {
            testContext.failNow("policy unsuppress operation is not successful");
        }
    }

    private static void assertSuppressPolicyTestResult(VertxTestContext testContext, JsonObject context)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            if (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.SUPPRESS_POLICY && message.body().getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.BASELINE.getName()))
            {

                assertEquals(context.getLong(AIOpsObject.OBJECT_ID), message.body().getLong(AIOpsConstants.ENTITY_ID));

                messageConsumer.unregister().onComplete(response -> testContext.completeNow());
            }
        });

    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testSuppressBaseLineMetricPolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test Anomaly CPU45\", \"policy.type\": \"Metric Baseline\", \"policy.context\": { \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 10, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ], \"WARNING\": [ { \"id\": 10000000000002 } ] } } }");

        context.put(POLICY_NAME, "cpu baseline 3" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("updated.latest.cpu.baseline", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    assertSuppressPolicyTestResult(testContext, context);

                    context.put(AIOpsObject.OBJECT_ID, object.getLong(ID)).put(PolicyEngineConstants.POLICY_SUPPRESSION_TIME, 10).put(POLICY_ID, IDS.getLong("updated.latest.cpu.baseline"));

                    TestUtil.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_POLICY_SUPPRESS).put(SESSION_ID, TestUtil.getSessionId()).put(User.USER_NAME, "admin")
                            .put(EVENT_CONTEXT, context));
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateMetricAnomalyPolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test Anomaly CPU\", \"policy.type\": \"Metric Anomaly\", \"policy.context\": { \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 10, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.title\": \"$$$severity$$$-$$$object.name$$$\", \"policy.message\": \"$$$counter$$$hasenteredinto$$$seveirty$$$statewithvalue$$$value$$$\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ], \"WARNING\": [ { \"id\": 10000000000002 } ] }, \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"MAJOR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"WARNING\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"CLEAR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] } }, \"Renotification\": { \"CRITICAL\": { \"timer.seconds\": 30, \"recipients\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] } } } }");

        context.put(POLICY_NAME, "cpu anomaly" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("cpu.anomaly", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateMetricAnomalyPolicyHavingObject(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var context = new JsonObject("{ \"policy.name\": \"Test Anomaly CPU 1\", \"policy.type\": \"Metric Anomaly\", \"policy.context\": { \"entity.type\": \"Monitor\", \"entities\": [], \"metric\": \"system.memory.used.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ] } } }");

        context.put(POLICY_NAME, "cpu anomaly 1" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("latest.anomaly.cpu", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateMetricAnomalyPolicyHavingGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test Anomaly CPU 2\", \"policy.type\": \"Metric Anomaly\", \"policy.context\": { \"entity.type\": \"Group\", \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ] } } }");

        context.put(POLICY_NAME, "cpu anomaly 2" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(10000000000018L).add(10000000000017L));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("update.anomaly.cpu", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCreateMetricAnomalyPolicyHavingTag(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.name\":\"Test Anomaly CPU 2\",\"policy.type\":\"Metric Anomaly\",\"policy.context\":{\"entity.type\":\"Group\",\"entities\":[],\"metric\":\"system.cpu.percent\",\"policy.metric.plugins\":[181,505],\"policy.trigger.time\":300,\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"policy.severity\":{\"CRITICAL\":{\"policy.condition\":\">=\",\"policy.threshold\":\"95\"},\"MAJOR\":{\"policy.condition\":\">=\",\"policy.threshold\":\"85\"},\"WARNING\":{\"policy.condition\":\">=\",\"policy.threshold\":\"75\"},\"CLEAR\":{\"policy.condition\":\"<\",\"policy.threshold\":\"75\"}},\"filters\":{\"data.filter\":{}}},\"policy.email.notification.recipients\":[],\"policy.renotify\":\"yes\",\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.renotification.timer.seconds\":0,\"policy.actions\":{\"CRITICAL\":[10000000000001]}}");

        context.put(POLICY_NAME, "cpu anomaly 3 " + System.currentTimeMillis());

        TagConfigStore.getStore().addItems(new JsonArray().add("Anomaly Tag"), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER);

        context.getJsonObject(POLICY_CONTEXT).put(ENTITY_TYPE, Tag.TAG).put(ENTITIES, new JsonArray().add("Anomaly Tag"));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("cpu.anomaly.tag", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCreateMetricForecastPolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.name\": \"Test Anomaly CPU\",\"policy.type\": \"Forecast\",\"policy.context\": {\"entities\": [],\"metric\": \"system.cpu.percent\",\"policy.metric.plugins\": [181,505],\"policy.trigger.time\": 300,\"policy.trigger.occurrences\": 1,\"policy.auto.clear.timer.seconds\": 10,\"policy.severity\": {\"CRITICAL\": {\"policy.condition\": \"above\",\"policy.threshold\": 20}},\"evaluation.window\": \"-48h\",\"filters\": {\"data.filter\": {}}},\"policy.email.notification.recipients\": [],\"policy.monitor.polling.failed.notification.status\": \"no\",\"policy.actions\": {\"Runbook\": {\"CRITICAL\": [{\"id\": 10000000000002}],\"WARNING\": [{\"id\": 10000000000002}]}}}");

        context.put(POLICY_NAME, "cpu forecast" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("cpu.forecast", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCreateMetricForecastPolicyHavingObject(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var context = new JsonObject("{\"policy.name\": \"Test Anomaly CPU 1\",\"policy.type\": \"Forecast\",\"policy.context\": {\"entity.type\": \"Monitor\",\"entities\": [],\"metric\": \"system.memory.used.percent\",\"policy.metric.plugins\": [181,505],\"policy.trigger.time\": 300,\"policy.trigger.occurrences\": 1,\"policy.auto.clear.timer.seconds\": 0,\"policy.severity\": {\"CRITICAL\": {\"policy.condition\": \"above\",\"policy.threshold\": 20}},\"evaluation.window\": \"-48h\",\"filters\": {\"data.filter\": {}}},\"policy.email.notification.recipients\": [],\"policy.monitor.polling.failed.notification.status\": \"no\",\"policy.actions\": {\"Runbook\": {\"CRITICAL\": [{\"id\": 10000000000002}]}}}");

        context.put(POLICY_NAME, "cpu forecast 1" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("latest.forecast.cpu", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCreateMetricForecastPolicyHavingGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.name\": \"Test Anomaly CPU 2\",\"policy.type\": \"Forecast\",\"policy.context\": {\"entity.type\": \"Group\",\"entities\": [],\"metric\": \"system.cpu.percent\",\"policy.metric.plugins\": [181,505],\"policy.trigger.time\": 300,\"policy.trigger.occurrences\": 1,\"policy.auto.clear.timer.seconds\": 0,\"policy.severity\": {\"CRITICAL\": {\"policy.condition\": \"above\",\"policy.threshold\": 20}},\"evaluation.window\": \"-48h\",\"filters\": {\"data.filter\": {}}},\"policy.email.notification.recipients\": [],\"policy.monitor.polling.failed.notification.status\": \"no\",\"policy.actions\": {\"Runbook\": {\"CRITICAL\": [{\"id\": 10000000000001}]}}}");

        context.put(POLICY_NAME, "cpu forecast 2" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(10000000000018L).add(10000000000017L));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("update.forecast.cpu", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCreateMetricForecastPolicyHavingTag(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.name\": \"Test Anomaly CPU\",\"policy.type\": \"Forecast\",\"policy.context\": {\"entities\": [],\"metric\": \"system.cpu.percent\",\"policy.metric.plugins\": [181,505],\"policy.trigger.time\": 300,\"policy.trigger.occurrences\": 1,\"policy.auto.clear.timer.seconds\": 10,\"policy.severity\": {\"CRITICAL\": {\"policy.condition\": \"above\",\"policy.threshold\": 20}},\"evaluation.window\": \"-48h\",\"filters\": {\"data.filter\": {}}},\"policy.email.notification.recipients\": [],\"policy.renotify\": \"no\",\"policy.monitor.polling.failed.notification.status\": \"no\",\"policy.renotification.timer.seconds\": 10,\"policy.actions\": {\"CRITICAL\": [10000000000002],\"WARNING\": [10000000000002]}}");

        TagConfigStore.getStore().addItems(new JsonArray().add("Forecast Tag"), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER);

        context.getJsonObject(POLICY_CONTEXT).put(ENTITY_TYPE, Tag.TAG).put(ENTITIES, new JsonArray().add("Forecast Tag"));

        context.put(POLICY_NAME, "cpu forecast tag" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("cpu.forecast.tag", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCreateBaselinePolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test Anomaly CPU\", \"policy.type\": \"Metric Baseline\", \"policy.context\": { \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 10, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ], \"WARNING\": [ { \"id\": 10000000000002 } ] }, \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"WARNING\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"MAJOR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] } } } }");

        context.put(POLICY_NAME, "cpu baseline" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("cpu.baseline", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testCreateBaselinePolicyHavingObject(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var context = new JsonObject("{ \"policy.name\": \"Test Anomaly CPU 1\", \"policy.type\": \"Metric Baseline\", \"policy.context\": { \"entity.type\": \"Monitor\", \"entities\": [], \"metric\": \"system.memory.used.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ] } } }");

        context.put(POLICY_NAME, "cpu basline 1" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("latest.baseline.cpu", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testCreateBaselinePolicyHavingGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test Anomaly CPU 2\", \"policy.type\": \"Metric Baseline\", \"policy.context\": { \"entity.type\": \"Group\", \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000001 } ] } } }");

        context.put(POLICY_NAME, "cpu baseline 2" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(10000000000018L).add(10000000000017L));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("update.baseline.cpu", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testCreateBaselinePolicyHavingTag(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.name\":\"Test Anomaly CPU\",\"policy.type\":\"Forecast\",\"policy.context\":{\"entities\":[],\"metric\":\"system.cpu.percent\",\"policy.metric.plugins\":[181,505],\"policy.trigger.time\":300,\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":10,\"policy.severity\":{\"CRITICAL\":{\"policy.condition\":\">=\",\"policy.threshold\":\"95\"},\"MAJOR\":{\"policy.condition\":\">=\",\"policy.threshold\":\"85\"},\"WARNING\":{\"policy.condition\":\">=\",\"policy.threshold\":\"75\"},\"CLEAR\":{\"policy.condition\":\"<\",\"policy.threshold\":\"75\"}},\"filters\":{\"data.filter\":{}}},\"policy.email.notification.recipients\":[],\"policy.renotify\":\"no\",\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.renotification.timer.seconds\":10,\"policy.actions\":{\"CRITICAL\":[10000000000002],\"WARNING\":[10000000000002]}}");

        TagConfigStore.getStore().addItems(new JsonArray().add("Baseline Tag"), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER);

        context.getJsonObject(POLICY_CONTEXT).put(ENTITY_TYPE, Tag.TAG).put(ENTITIES, new JsonArray().add("Baseline Tag"));

        context.put(POLICY_NAME, "cpu forecast tag" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("cpu.forecast.tag", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testAIOpsPolicyOnDatastoreFlushOperation(VertxTestContext testContext) throws Exception
    {
        Assertions.assertNotNull(linuxObject);

        Assertions.assertNotNull(object);

        for (var i = 0; i < 50; i++)
        {
            Bootstrap.vertx().eventBus().send(EVENT_DATASTORE_FLUSH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(NMSConstants.OBJECT, new JsonArray().add(linuxObject.getInteger(AIOpsObject.OBJECT_ID))).put("plugin", "88-linux")));

            Bootstrap.vertx().eventBus().send(EVENT_DATASTORE_FLUSH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(NMSConstants.OBJECT, new JsonArray().add(object.getInteger(AIOpsObject.OBJECT_ID))).put("plugin", "181-windows")));
        }

        TestUtil.vertx().setPeriodic(1000, timer ->
                Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_AIOPS_METRIC_POLICY_MANAGER_TEST, new JsonObject(), reply ->
                {
                    if (reply.result().body() != null)
                    {
                        var pluginByObjects = reply.result().body().getJsonObject("plugin.objects");

                        Assertions.assertFalse(pluginByObjects.isEmpty());

                        TestUtil.vertx().cancelTimer(timer);

                        testContext.completeNow();
                    }
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testCreateMetricPolicyHavingInstanceFilter(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.name\":\"Instance Filter\",\"policy.type\":\"Forecast\",\"policy.rolling.window\":null,\"policy.context\":{\"entities\":[],\"metric\":\"interface~packets\",\"policy.metric.plugins\":[201],\"instance.type\":\"interface\",\"policy.trigger.time\":300,\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"policy.severity\":{\"CRITICAL\":{\"policy.condition\":\">=\",\"policy.threshold\":\"100000\"},\"MAJOR\":{\"policy.condition\":\">=\",\"policy.threshold\":\"90000\"},\"WARNING\":{\"policy.condition\":\">=\",\"policy.threshold\":\"80000\"},\"CLEAR\":{\"policy.condition\":\"<\",\"policy.threshold\":\"80000\"}},\"filters\":{\"data.filter\":{\"operator\":\"and\",\"filter\":\"include\",\"groups\":[{\"filter\":\"include\",\"operator\":\"and\",\"conditions\":[{\"operand\":\"interface~link.type\",\"operator\":\"=\",\"value\":\"WAN\"}]}]}}},\"policy.email.notification.recipients\":[],\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.actions\":{}}");

        context.put(POLICY_NAME, "instance.forecast" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("instance.forecast", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testInstancesByObject(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var event = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(networkObject);

        var metric = new JsonObject();

        var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(networkObject.getLong(ID)));

        LOGGER.info("testInstancesByObject: items: " + items.encodePrettily());

        for (var i = 0; i < items.size(); i++)
        {
            var item = items.getJsonObject(i);

            if (item.getString(Metric.METRIC_NAME).equalsIgnoreCase(event.getString(Metric.METRIC_NAME)))
            {
                metric.mergeIn(item);

                break;
            }
        }

        Assertions.assertFalse(metric.isEmpty());

        event.mergeIn(metric);

        Bootstrap.vertx().eventBus().send(EVENT_AIOPS_METRIC_POLICY_MANAGER, event);

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        Bootstrap.vertx().eventBus().send(EVENT_AIOPS_METRIC_POLICY_MANAGER, event.put(TAGS, TagConfigStore.getStore().getIdsByItems(new JsonArray().add("Forecast Tag"))));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        Bootstrap.vertx().eventBus().send(EVENT_AIOPS_METRIC_POLICY_MANAGER, event.put(TAGS, TagConfigStore.getStore().getIdsByItems(new JsonArray().add("Baseline Tag"))));

        Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_AIOPS_METRIC_POLICY_MANAGER_TEST, new JsonObject(), reply ->
        {
            if (reply.result().body() != null && reply.result().body() != null)
            {
                var objects = reply.result().body().getJsonObject("instance.objects");

                LOGGER.info("testInstancesByObject: objects: " + objects.encodePrettily());

                Assertions.assertFalse(objects.isEmpty());

                testContext.completeNow();
            }
            else
            {
                testContext.failNow("result empty");
            }
        });
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testAnomalyMetricPolicyInspection(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testAIOpsPolicyInspection");

        var timestamp = DateTimeUtil.currentSeconds();

        var dataPoints = new JsonArray();

        var objectId = object.getInteger(AIOpsObject.OBJECT_ID);

        for (var index = 0; index < 3; index++)
        {
            dataPoints.add(new JsonObject().put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg", 50.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "upperbound", 50.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "lowerbound", 27.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "anomaly", 0.0)
                    .put(TIME_STAMP, timestamp));
        }

        var metric = prepareContext(dataPoints, new JsonObject(), context, IDS.getLong("cpu.anomaly"), timestamp);

        var item = new JsonObject("{\"metric.name\":\"Windows\",\"metric.polling.time\":300,\"metric.plugin\":\"windows\",\"metric.state\":\"ENABLE\",\"metric.type\":\"Windows\",\"metric.context\":{\"plugin.id\":181,\"metric.polling.min.time\":300,\"timeout\":60},\"metric.object\":377656204831,\"metric.category\":\"Server\",\"metric.credential.profile\":377656204829,\"metric.credential.profile.protocol\":\"Powershell\",\"metric.discovery.method\":\"REMOTE\",\"_type\":\"1\",\"id\":1234567}");

        item.put(Metric.METRIC_OBJECT, object.getLong(ID));

        Bootstrap.configDBService().upsert(DBConstants.TBL_METRIC, item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
        {

            if (asyncResult.succeeded())
            {
                MetricConfigStore.getStore().addItem(asyncResult.result());

                assertAIOpsPolicyResult(PolicyType.ANOMALY.getName(), testContext, IDS.getLong("cpu.anomaly"), object.getLong(ID));

                Bootstrap.vertx().eventBus().send(EVENT_AIOPS_METRIC_POLICY_RESPONSE, metric);
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testAnomalyMetricPolicyInspectionHavingTag(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testAIOpsPolicyInspection");

        var timestamp = DateTimeUtil.currentSeconds();

        var dataPoints = new JsonArray();

        var objectId = object.getInteger(AIOpsObject.OBJECT_ID);

        for (var index = 0; index < 3; index++)
        {
            dataPoints.add(new JsonObject().put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg", 50.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "upperbound", 50.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "lowerbound", 27.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "anomaly", 0.0)
                    .put(TIME_STAMP, timestamp));
        }

        var metric = prepareContext(dataPoints, new JsonObject(), context, IDS.getLong("cpu.anomaly.tag"), timestamp);

        var item = new JsonObject("{\"metric.name\":\"Windows\",\"metric.polling.time\":300,\"metric.plugin\":\"windows\",\"metric.state\":\"ENABLE\",\"metric.type\":\"Windows\",\"metric.context\":{\"plugin.id\":181,\"metric.polling.min.time\":300,\"timeout\":60},\"metric.object\":377656204831,\"metric.category\":\"Server\",\"metric.credential.profile\":377656204829,\"metric.credential.profile.protocol\":\"Powershell\",\"metric.discovery.method\":\"REMOTE\",\"_type\":\"1\",\"id\":1234567}");

        item.put(Metric.METRIC_OBJECT, object.getLong(ID));

        Bootstrap.configDBService().upsert(DBConstants.TBL_METRIC, item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
        {

            if (asyncResult.succeeded())
            {
                MetricConfigStore.getStore().addItem(asyncResult.result());

                assertAIOpsPolicyResult(PolicyType.ANOMALY.getName(), testContext, IDS.getLong("cpu.anomaly.tag"), object.getLong(ID));

                Bootstrap.vertx().eventBus().send(EVENT_AIOPS_METRIC_POLICY_RESPONSE, metric);
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testTriggerNotificationAnomalyMetricPolicy(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testAIOpsPolicyInspection");

        var timestamp = DateTimeUtil.currentSeconds();

        var dataPoints = new JsonArray();

        var objectId = object.getInteger(AIOpsObject.OBJECT_ID);

        for (var index = 0; index < 3; index++)
        {
            dataPoints.add(new JsonObject().put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg", 50.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "upperbound", 50.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "lowerbound", 27.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "anomaly", 1.0)
                    .put(TIME_STAMP, timestamp + index));
        }

        Bootstrap.vertx().eventBus().send(EVENT_AIOPS_METRIC_POLICY_RESPONSE, prepareContext(dataPoints, new JsonObject(), context, IDS.getLong("cpu.anomaly"), timestamp));

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_NOTIFICATION, message ->
        {

            var event = message.body();

            if (!event.isEmpty() && event.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).contains("<EMAIL>"))   // check notification received for triggered policy or flap changed
            {
                Assertions.assertEquals(Notification.NotificationType.EMAIL.getName(), message.body().getString(NOTIFICATION_TYPE));

                Assertions.assertNotNull(event.getJsonObject(Notification.EMAIL_NOTIFICATION_CONTENT));

                var content = event.getJsonObject(Notification.EMAIL_NOTIFICATION_CONTENT).toString();

                Assertions.assertFalse(content.isEmpty());

                messageConsumer.unregister(asyncResult -> testContext.completeNow());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testAnomalyMetricPolicyInspectionViolation(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testAIOpsPolicyInspection");

        var timestamp = DateTimeUtil.currentSeconds();

        var dataPoints = new JsonArray();

        var objectId = object.getInteger(AIOpsObject.OBJECT_ID);

        for (var index = 0; index < 3; index++)
        {
            dataPoints.add(new JsonObject().put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg", 50.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "upperbound", 50.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "lowerbound", 27.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "anomaly", 1.0)
                    .put(TIME_STAMP, timestamp + index));
        }

        var result = prepareContext(dataPoints, new JsonObject(), context, IDS.getLong("cpu.anomaly"), timestamp);

        assertAIOpsPolicyResult(PolicyType.ANOMALY.getName(), testContext, IDS.getLong("cpu.anomaly"), object.getLong(ID));

        Bootstrap.vertx().eventBus().send(EVENT_AIOPS_METRIC_POLICY_RESPONSE, result);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testForecastMetricPolicyInspection(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testAIOpsPolicyInspection");

        var timestamp = DateTimeUtil.currentSeconds();

        var dataPoints = new JsonArray();

        var objectId = object.getInteger(AIOpsObject.OBJECT_ID);

        for (var index = 0; index < 3; index++)
        {
            dataPoints.add(new JsonObject().put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg", 50.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "upperbound", 50.16)
                    .put(objectId + CARET_SEPARATOR + "system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "lowerbound", 27.16)
                    .put(VisualizationConstants.TIMESTAMP, (System.currentTimeMillis() + ((index + 1) * 12 * 60 * 60 * 1000))));
        }

        var result = prepareContext(dataPoints, new JsonObject(), context, IDS.getLong("cpu.forecast"), timestamp);

        assertAIOpsPolicyResult(PolicyType.FORECAST.getName(), testContext, IDS.getLong("cpu.forecast"), object.getLong(ID));

        Bootstrap.vertx().eventBus().send(EVENT_AIOPS_METRIC_POLICY_RESPONSE, result);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testBaseLinetMetricPolicyInspection(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testAIOpsPolicyInspection");

        var timestamp = DateTimeUtil.currentSeconds();

        var dataPoints = new JsonArray();

        var objectId = object.getInteger(AIOpsObject.OBJECT_ID);

        for (var index = 0; index < 3; index++)
        {
            dataPoints.add(new JsonObject().put("monitor", objectId)
                    .put("system.cpu.percent" + CARET_SEPARATOR + "avg", 0.5)
                    .put("system.cpu.percent" + CARET_SEPARATOR + "avg" + CARET_SEPARATOR + "baseline", 0.5));
        }

        var result = prepareContext(dataPoints, new JsonObject(), context, IDS.getLong("cpu.baseline"), timestamp);

        assertAIOpsPolicyResult(PolicyType.BASELINE.getName(), testContext, IDS.getLong("cpu.baseline"), object.getLong(ID));

        Bootstrap.vertx().eventBus().send(EVENT_AIOPS_METRIC_POLICY_RESPONSE, result);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testTriggerEmailNotificationBaselineMetric(VertxTestContext testContext) throws Exception
    {
        var context = new JsonObject("{\"value\":9399795712,\"severity\":\"MAJOR\",\"policy.threshold\":9367268440,\"instance\":\"\",\"metric.type\":\"Linux\",\"event.timestamp\":1731511818,\"object.category\":\"Server\",\"plugin.id\":88,\"entity.id\":58977634082,\"policy.key\":\"58977634082``||``Metric Baseline``||``system.memory.used.bytes\",\"metric\":\"system.memory.used.bytes\",\"policy.type\":\"Metric Baseline\",\"id\":58977634095,\"object.id\":1,\"object.name\":\"yash-ThinkPad-T14-Gen-1\",\"object.type\":\"Linux\",\"object.ip\":\"************\"}");

        var policy = new JsonObject("{ \"policy.name\": \"cpu baseline\", \"policy.type\": \"Metric Baseline\", \"policy.rolling.window\": null, \"policy.scheduled\": \"no\", \"policy.context\": { \"entity.type\": \"Monitor\", \"entities\": [ 58977634082 ], \"metric\": \"system.memory.used.bytes\", \"policy.metric.plugins\": [ 88 ], \"threshold.type\": \"absolute\", \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"policy.severity\": { \"MAJOR\": { \"policy.condition\": \"above\", \"policy.threshold\": \"30\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.title\": \"$$$severity$$$ - $$$object.name$$$\", \"policy.message\": \"$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$\", \"policy.actions\": { \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"MAJOR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"WARNING\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"CLEAR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] } } }, \"policy.archived\": \"no\", \"policy.creation.time\": 1731509857, \"policy.state\": \"yes\", \"_type\": \"1\", \"id\": 58977634095 }");

        var method = PolicyEngineConstants.class.getDeclaredMethod("triggerAction", String.class, boolean.class, JsonObject.class, JsonObject.class, Set.class, Set.class, Map.class, long.class, String.class, Logger.class, Set.class, StringBuilder.class);

        method.setAccessible(true);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_NOTIFICATION, message ->
        {

            var event = message.body();

            if (!event.isEmpty() && event.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).contains("<EMAIL>"))   // check notification received for triggered policy or flap changed
            {
                Assertions.assertEquals(Notification.NotificationType.EMAIL.getName(), message.body().getString(NOTIFICATION_TYPE));

                Assertions.assertNotNull(event.getJsonObject(Notification.EMAIL_NOTIFICATION_CONTENT));

                var content = event.getJsonObject(Notification.EMAIL_NOTIFICATION_CONTENT).toString();

                Assertions.assertFalse(content.isEmpty());

                messageConsumer.unregister(asyncResult -> testContext.completeNow());
            }
        });

        Assertions.assertNotNull(IDS.getLong("cpu.baseline"));

        method.invoke(method, "MAJOR", true, policy, context, new HashSet<String>(), new HashSet<String>(), new HashMap<String, Set<String>>(), ObjectConfigStore.getStore().getItemByIP("************"), "system.memory.used.bytes of yash-ThinkPad-T14-Gen-1 has entered into MAJOR state, as current metric value 9399795712  is above the auto-detected baseline 9367268440", LOGGER, new HashSet<String>(), new StringBuilder(EMPTY_VALUE));
    }

    private JsonObject prepareContext(JsonArray dataPoints, JsonObject mergedResult, JsonObject context, long policyId, long timestamp) throws Exception
    {
        for (var index = 0; index < dataPoints.size(); index++)
        {
            dataPoints.getJsonObject(index).forEach(result -> ((JsonArray) mergedResult.getMap().computeIfAbsent(result.getKey(), item -> new JsonArray())).add(result.getValue()));
        }

        return new JsonObject()
                .put(RESULT, VisualizationConstants.packResult(mergedResult, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), context.getLong(QUERY_ID), context.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes())
                .put(POLICY_ID, policyId)
                .put(QUERY_PROGRESS, 50).put(METRIC, "system.cpu.percent")
                .put(PLUGIN_ID, 181).put(EVENT_TIMESTAMP, timestamp);
    }

    private void assertAIOpsPolicyResult(String policyType, VertxTestContext testContext, long policyId, long objectId)
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_AIOPS_METRIC_POLICY_MANAGER_TEST, message ->
        {
            try
            {
                var event = message.body();

                if (event.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.ANOMALY.getName()) || event.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.FORECAST.getName()) || event.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.BASELINE.getName()))
                {

                    Assertions.assertEquals(policyId, event.getLong(ID));

                    Assertions.assertEquals(181, event.getInteger(PLUGIN_ID));

                    Assertions.assertEquals(policyType, event.getString(POLICY_TYPE));

                    Assertions.assertEquals(objectId, event.getLong(ENTITY_ID));

                    Assertions.assertNotNull(event.getValue(SEVERITY));

                    Assertions.assertNotNull(event.getValue(MetricPolicy.POLICY_THRESHOLD));

                    Assertions.assertNotNull(event.getValue(VALUE));

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }

}
